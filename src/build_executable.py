import os
import sys
import shutil
import subprocess
import platform

def build_executable():
    """
    Build a standalone executable for the DICOM viewer
    """
    print("Building standalone executable for DICOM viewer...")
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
    except ImportError:
        print("PyInstaller is not installed. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # Check if required packages are installed
    required_packages = ["pywebview", "pydicom", "numpy", "pyedflib"]
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            print(f"{package} is not installed. Installing...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
    
    # Create a spec file for PyInstaller
    spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['standalone_app.py'],
    pathex=[],
    binaries=[],
    datas=[('templates/standalone.html', 'templates')],
    hiddenimports=['tkinter'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PSG_DICOM_Viewer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico',
)
    """
    
    # Create a simple icon for the application
    if not os.path.exists("icon.ico"):
        print("Creating a default icon...")
        try:
            from PIL import Image, ImageDraw
            
            # Create a simple icon
            img = Image.new('RGB', (256, 256), color=(0, 120, 212))
            d = ImageDraw.Draw(img)
            d.rectangle([(50, 50), (206, 206)], fill=(255, 255, 255))
            d.rectangle([(70, 70), (186, 186)], fill=(0, 120, 212))
            
            # Save as ICO
            img.save("icon.ico")
        except ImportError:
            print("PIL not installed, skipping icon creation.")
    
    # Write the spec file
    with open("standalone.spec", "w") as f:
        f.write(spec_content)
    
    # Build the executable
    print("Building executable with PyInstaller...")
    subprocess.check_call([
        sys.executable, 
        "-m", 
        "PyInstaller", 
        "standalone.spec", 
        "--clean"
    ])
    
    print("Build complete!")
    print(f"Executable is located in the 'dist' folder.")

if __name__ == "__main__":
    # Check if we're on Windows
    if platform.system() != "Windows":
        print("This script is intended to be run on Windows.")
        print(f"Current platform: {platform.system()}")
        sys.exit(1)
    
    build_executable()
