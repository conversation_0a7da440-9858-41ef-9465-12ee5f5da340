import webview
import os
import sys
import tempfile
import uuid
import shutil
import json
import pydicom
from pathlib import Path
import numpy as np
from tkinter import Tk, filedialog

# Try to import pyedflib for EDF support
try:
    import pyedflib
    EDF_SUPPORT = True
except ImportError:
    EDF_SUPPORT = False
    print("Warning: pyedflib not installed. EDF files will not be supported.")

# Create a temporary directory to store files
TEMP_FOLDER = os.path.join(tempfile.gettempdir(), 'dicom-viewer-standalone')
os.makedirs(TEMP_FOLDER, exist_ok=True)

# Dictionary to keep track of the current study
current_study = {
    "id": None,
    "path": None,
    "files": [],
    "signals": [],
    "annotations": [],
    "patient_info": {}
}

def get_dicom_sop_title(sop_class_uid, trim=True):
    """
    Returns the official DICOM title for a given SOP Class UID.
    """
    sop_mapping = {
        "1.2.840.10008.5.1.4.1.1.9.7.4": "Sleep Electroencephalogram Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.7.3": "Electrooculogram Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.7.2": "Electromyogram Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.8.1": "Body Position Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.1.2": "General ECG Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.6.1": "Respiratory Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.6.2": "Multi-channel Respiratory Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.4.2": "General Audio Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.5.1": "Arterial Pulse Waveform Storage",
    }

    value = sop_mapping.get(sop_class_uid, "Unknown SOP Class")

    if trim:
        value = value.replace(' Storage', '')
    return value


def detect_file_type(file_path):
    """
    Detect the type of file (DICOM or EDF)

    Args:
        file_path (str): Path to the file

    Returns:
        str: 'dicom', 'edf', or 'unknown'
    """
    file_path = Path(file_path)

    # Check file extension first
    if file_path.suffix.lower() == '.dcm':
        return 'dicom'
    elif file_path.suffix.lower() in ['.edf', '.edf+']:
        return 'edf'

    # Try to read as DICOM
    try:
        pydicom.dcmread(file_path)
        return 'dicom'
    except:
        pass

    # Try to read as EDF if pyedflib is available
    if EDF_SUPPORT:
        try:
            with pyedflib.EdfReader(str(file_path)) as f:
                # If we can open it, it's an EDF file
                return 'edf'
        except:
            pass

    return 'unknown'


class DicomViewerAPI:
    def __init__(self):
        self.selected_folder = None
        self.selected_files = []
    
    def select_folder(self):
        """
        Open a folder selection dialog and return the selected files
        """
        try:
            # Hide the main Tkinter window
            root = Tk()
            root.withdraw()
            
            # Open the folder selection dialog
            folder_path = filedialog.askdirectory(title="Select folder containing DICOM or EDF files")

            if not folder_path:
                return {"success": False, "error": "No folder selected"}

            self.selected_folder = folder_path

            # Get all DICOM and EDF files in the folder
            self.selected_files = []
            for file_path in Path(folder_path).glob("**/*.dcm"):
                self.selected_files.append(str(file_path))
            for file_path in Path(folder_path).glob("**/*.edf"):
                self.selected_files.append(str(file_path))

            if not self.selected_files:
                return {"success": False, "error": "No DICOM or EDF files found in the selected folder"}
            
            # Return just the filenames for display
            filenames = [os.path.basename(f) for f in self.selected_files]
            
            return {"success": True, "files": filenames}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def process_files(self):
        """
        Process the selected files and create a study
        """
        try:
            if not self.selected_files:
                return {"success": False, "error": "No files selected"}
            
            # Create a new study ID and directory
            study_id = str(uuid.uuid4())
            study_dir = os.path.join(TEMP_FOLDER, study_id)
            os.makedirs(study_dir, exist_ok=True)
            
            # Copy files to the study directory
            for file_path in self.selected_files:
                filename = os.path.basename(file_path)
                dest_path = os.path.join(study_dir, filename)
                shutil.copy2(file_path, dest_path)
            
            # Update current study
            current_study["id"] = study_id
            current_study["path"] = study_dir
            
            # Process the study files
            self._process_study_files(study_dir)
            
            return {"success": True}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _process_study_files(self, study_path):
        """
        Process all files in the study directory
        """
        current_study["files"] = []
        current_study["signals"] = []
        current_study["annotations"] = []
        
        annotation_file = None
        
        # Process each file
        all_files = list(Path(study_path).glob("*.dcm")) + list(Path(study_path).glob("*.edf"))
        edf_annotation_files = []

        for file_path in all_files:
            file_type = detect_file_type(file_path)

            try:
                if file_type == 'dicom':
                    ds = pydicom.dcmread(file_path)

                    # Extract patient info if available
                    if hasattr(ds, 'PatientName'):
                        current_study["patient_info"]["name"] = str(ds.PatientName)
                    if hasattr(ds, 'PatientID'):
                        current_study["patient_info"]["id"] = ds.PatientID
                    if hasattr(ds, 'PatientBirthDate'):
                        current_study["patient_info"]["birthdate"] = ds.PatientBirthDate
                    if hasattr(ds, 'PatientSex'):
                        current_study["patient_info"]["sex"] = ds.PatientSex

                    # Get file type
                    if hasattr(ds, 'SOPClassUID'):
                        file_type_name = get_dicom_sop_title(ds.SOPClassUID)
                    elif "annotations" in file_path.name.lower():
                        file_type_name = "Annotations"
                    else:
                        file_type_name = "DICOM"

                    # Add to files list
                    current_study["files"].append({
                        "filename": file_path.name,
                        "path": str(file_path),
                        "type": file_type_name,
                        "size": file_path.stat().st_size
                    })

                    # Check if this is an annotation file
                    if "annotations" in file_path.name.lower():
                        annotation_file = file_path
                    else:
                        # This is a signal file
                        current_study["signals"].append({
                            "type": file_type_name,
                            "path": str(file_path),
                            "channels": len(ds.WaveformSequence) if hasattr(ds, 'WaveformSequence') else 0
                        })

                elif file_type == 'edf' and EDF_SUPPORT:
                    with pyedflib.EdfReader(str(file_path)) as f:
                        # Try to get patient information from EDF header
                        if hasattr(f, 'getPatientName'):
                            current_study["patient_info"]["name"] = f.getPatientName()
                        if hasattr(f, 'getPatientCode'):
                            current_study["patient_info"]["id"] = f.getPatientCode()

                        # Check if this EDF file has annotations
                        try:
                            onset, duration, description = f.readAnnotations()
                            if len(onset) > 0:
                                edf_annotation_files.append(file_path)
                        except:
                            pass  # No annotations in this file

                    file_type_name = "EDF Signals"

                    # Add to files list
                    current_study["files"].append({
                        "filename": file_path.name,
                        "path": str(file_path),
                        "type": file_type_name,
                        "size": file_path.stat().st_size
                    })

                    # Add as signal file (all EDF channels in one group)
                    with pyedflib.EdfReader(str(file_path)) as f:
                        current_study["signals"].append({
                            "type": file_type_name,
                            "path": str(file_path),
                            "channels": f.signals_in_file
                        })
                else:
                    # Unknown file type
                    current_study["files"].append({
                        "filename": file_path.name,
                        "path": str(file_path),
                        "type": "Unknown",
                        "size": file_path.stat().st_size
                    })

            except Exception as e:
                # If we can't read the file, just add basic file info
                current_study["files"].append({
                    "filename": file_path.name,
                    "path": str(file_path),
                    "type": "Unknown",
                    "size": file_path.stat().st_size,
                    "error": str(e)
                })
        
        # Extract annotations from DICOM and EDF files
        # DICOM annotations
        if annotation_file:
            try:
                ds = pydicom.dcmread(annotation_file)
                if hasattr(ds, 'ContentSequence'):
                    for item in ds.ContentSequence:
                        if hasattr(item, 'ContentSequence') and len(item.ContentSequence) >= 2:
                            event_type = item.ContentSequence[0].ConceptNameCodeSequence[0].CodeValue
                            event_name = item.ContentSequence[0].ConceptNameCodeSequence[0].CodeMeaning
                            start_time = float(item.ContentSequence[1].MeasuredValueSequence[0].NumericValue)
                            duration = float(item.ContentSequence[2].MeasuredValueSequence[0].NumericValue)
                            current_study["annotations"].append({
                                "type": event_type,
                                "type_name": event_name,
                                "start": start_time,
                                "duration": duration,
                                "end": start_time + duration
                            })
            except Exception as e:
                print(f"Error reading DICOM annotations: {str(e)}")

        # EDF+ annotations
        if EDF_SUPPORT:
            for edf_file in edf_annotation_files:
                try:
                    with pyedflib.EdfReader(str(edf_file)) as f:
                        onset, duration, description = f.readAnnotations()
                        for i in range(len(onset)):
                            # Convert EDF+ annotation to our format
                            start_time = float(onset[i])
                            dur = float(duration[i]) if duration[i] > 0 else 0.0
                            desc = description[i].decode('utf-8') if isinstance(description[i], bytes) else str(description[i])

                            current_study["annotations"].append({
                                "type": "EDF_ANNOTATION",
                                "type_name": desc,
                                "start": start_time,
                                "duration": dur,
                                "end": start_time + dur
                            })
                except Exception as e:
                    print(f"Error reading EDF+ annotations from {edf_file}: {str(e)}")
    
    def get_study_data(self):
        """
        Get the current study data
        """
        try:
            if not current_study["id"]:
                return {"success": False, "error": "No study loaded"}
            
            return {
                "success": True,
                "data": {
                    "patient_info": current_study["patient_info"],
                    "files": current_study["files"],
                    "signals": current_study["signals"],
                    "annotations": current_study["annotations"]
                }
            }
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_signal_data(self, signal_type, start_time, window_size):
        """
        Get signal data for a specific time window
        """
        try:
            if not current_study["id"]:
                return {"success": False, "error": "No study loaded"}

            # Find the signal file
            signal_file = next((s for s in current_study["signals"] if s["type"] == signal_type), None)
            if not signal_file:
                return {"success": False, "error": f"Signal type {signal_type} not found"}

            file_path = signal_file["path"]
            file_type = detect_file_type(file_path)

            if file_type == 'dicom':
                return self._get_dicom_signal_data(file_path, start_time, window_size)
            elif file_type == 'edf' and EDF_SUPPORT:
                return self._get_edf_signal_data(file_path, start_time, window_size)
            else:
                return {"success": False, "error": f"Unsupported file type: {file_type}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _get_dicom_signal_data(self, file_path, start_time, window_size):
        """Extract signal data from DICOM file"""
        try:
            ds = pydicom.dcmread(file_path)

            waveform_data = []
            for waveform in ds.WaveformSequence:
                channel_name = waveform.ChannelDefinitionSequence[0].ChannelLabel
                sampling_rate = waveform.SamplingFrequency

                # Calculate sample indices based on time window
                start_sample = int(start_time * sampling_rate)
                end_sample = int((start_time + window_size) * sampling_rate)

                # Extract actual signal data
                if waveform.WaveformSampleInterpretation == 'SS':
                    full_data = np.frombuffer(waveform.WaveformData, dtype=np.int16)
                elif waveform.WaveformSampleInterpretation == 'SL':
                    full_data = np.frombuffer(waveform.WaveformData, dtype=np.int32)
                else:
                    raise Exception(f"Can't read {waveform.WaveformSampleInterpretation} only SS and SL are supported")

                # Only extract the needed portion of data
                data = full_data[start_sample:min(end_sample, len(full_data))]

                waveform_data.append({
                    "name": channel_name,
                    "sampling_rate": sampling_rate,
                    "data": data.tolist(),  # Only sending the requested window
                    "total_samples": len(full_data)  # Include total sample count for UI reference
                })

            return {"success": True, "data": waveform_data}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _get_edf_signal_data(self, file_path, start_time, window_size):
        """Extract signal data from EDF file"""
        if not EDF_SUPPORT:
            return {"success": False, "error": "EDF support not available"}

        try:
            waveform_data = []

            with pyedflib.EdfReader(file_path) as f:
                n_signals = f.signals_in_file

                for i in range(n_signals):
                    channel_name = f.getLabel(i)
                    sampling_rate = f.getSampleFrequency(i)

                    # Calculate sample indices based on time window
                    start_sample = int(start_time * sampling_rate)
                    end_sample = int((start_time + window_size) * sampling_rate)

                    # Read the signal data for the time window
                    total_samples = f.getNSamples()[i]
                    end_sample = min(end_sample, total_samples)

                    if start_sample < total_samples:
                        # Read the data for this time window
                        data = f.readSignal(i, start_sample, end_sample - start_sample)

                        waveform_data.append({
                            "name": channel_name,
                            "sampling_rate": float(sampling_rate),
                            "data": data.tolist(),
                            "total_samples": int(total_samples)
                        })

            return {"success": True, "data": waveform_data}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def clear_study(self):
        """
        Clear the current study
        """
        try:
            if current_study["id"]:
                study_path = current_study["path"]
                shutil.rmtree(study_path)
                
                # Reset current study
                current_study["id"] = None
                current_study["path"] = None
                current_study["files"] = []
                current_study["signals"] = []
                current_study["annotations"] = []
                current_study["patient_info"] = {}
                
                # Reset selected files
                self.selected_folder = None
                self.selected_files = []
                
                return {"success": True}
            else:
                return {"success": False, "error": "No study loaded"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def add_files(self):
        """
        Add more files to the current study
        """
        try:
            if not current_study["id"]:
                return {"success": False, "error": "No study loaded"}
            
            # Hide the main Tkinter window
            root = Tk()
            root.withdraw()
            
            # Open the file selection dialog
            file_paths = filedialog.askopenfilenames(
                title="Select DICOM files to add",
                filetypes=[("DICOM files", "*.dcm")]
            )
            
            if not file_paths:
                return {"success": False, "error": "No files selected"}
            
            # Copy files to the study directory
            for file_path in file_paths:
                filename = os.path.basename(file_path)
                dest_path = os.path.join(current_study["path"], filename)
                shutil.copy2(file_path, dest_path)
            
            # Re-process the study files
            self._process_study_files(current_study["path"])
            
            return {"success": True}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def delete_file(self, filename):
        """
        Delete a file from the current study
        """
        try:
            if not current_study["id"]:
                return {"success": False, "error": "No study loaded"}
            
            file_path = os.path.join(current_study["path"], filename)
            
            if not os.path.exists(file_path):
                return {"success": False, "error": "File not found"}
            
            # Delete the file
            os.remove(file_path)
            
            # Re-process the study files
            self._process_study_files(current_study["path"])
            
            return {"success": True}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def check_study_loaded(self):
        """
        Check if a study is currently loaded
        """
        return {"success": True, "loaded": current_study["id"] is not None}

def get_resource_path(relative_path):
    """
    Get the absolute path to a resource, works for dev and for PyInstaller
    """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def main():
    # Create the API instance
    api = DicomViewerAPI()
    
    # Get the path to the HTML file
    html_path = get_resource_path("templates/standalone.html")
    
    # Create the window
    window = webview.create_window(
        'PSG DICOM Viewer - Standalone',
        html_path,
        js_api=api,
        width=1200,
        height=800
    )
    
    # Start the webview
    webview.start(debug=True)

if __name__ == '__main__':
    main()
