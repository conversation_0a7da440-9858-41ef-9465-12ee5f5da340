from flask import Flask, render_template, request, jsonify, send_from_directory
import pydicom
import os
import json
from pathlib import Path
import numpy as np
import tempfile
import uuid
import shutil

# Try to import pyedflib for EDF support
try:
    import pyedflib
    EDF_SUPPORT = True
except ImportError:
    EDF_SUPPORT = False
    print("Warning: pyedflib not installed. EDF files will not be supported.")

app = Flask(__name__)

# Create a temporary directory to store uploaded files
UPLOAD_FOLDER = os.path.join(tempfile.gettempdir(), 'dicom-viewer-uploads')
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Dictionary to keep track of uploaded studies
uploaded_studies = {}

def get_dicom_sop_title(sop_class_uid, trim=True):
    """
    Returns the official DICOM title for a given SOP Class UID.

    Args:
        sop_class_uid (str): The SOP Class UID from the DICOM file

    Returns:
        str: The official DICOM title for the SOP Class
    """
    sop_mapping = {
        "1.2.840.10008.5.1.4.1.1.9.7.4": "Sleep Electroencephalogram Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.7.3": "Electrooculogram Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.7.2": "Electromyogram Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.8.1": "Body Position Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.1.2": "General ECG Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.6.1": "Respiratory Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.6.2": "Multi-channel Respiratory Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.4.2": "General Audio Waveform Storage",
        "1.2.840.10008.5.1.4.1.1.9.5.1": "Arterial Pulse Waveform Storage",
    }

    value = sop_mapping.get(sop_class_uid, "Unknown SOP Class")

    if trim:
        value = value.replace(' Storage', '')
    return value


def detect_file_type(file_path):
    """
    Detect the type of file (DICOM or EDF)

    Args:
        file_path (str): Path to the file

    Returns:
        str: 'dicom', 'edf', or 'unknown'
    """
    file_path = Path(file_path)

    # Check file extension first
    if file_path.suffix.lower() == '.dcm':
        return 'dicom'
    elif file_path.suffix.lower() in ['.edf', '.edf+']:
        return 'edf'

    # Try to read as DICOM
    try:
        pydicom.dcmread(file_path)
        return 'dicom'
    except:
        pass

    # Try to read as EDF if pyedflib is available
    if EDF_SUPPORT:
        try:
            with pyedflib.EdfReader(str(file_path)) as f:
                # If we can open it, it's an EDF file
                return 'edf'
        except:
            pass

    return 'unknown'


@app.route('/')
def index():
    return render_template('viewer.html')

@app.route('/api/upload-files', methods=['POST'])
def upload_files():
    if 'files[]' not in request.files:
        return jsonify({"error": "No files provided"}), 400

    files = request.files.getlist('files[]')

    if not files or files[0].filename == '':
        return jsonify({"error": "No files selected"}), 400

    # Create a new study ID and directory
    study_id = str(uuid.uuid4())
    study_dir = os.path.join(UPLOAD_FOLDER, study_id)
    os.makedirs(study_dir, exist_ok=True)

    # Save uploaded files
    file_paths = []
    for file in files:
        if file.filename and (file.filename.endswith('.dcm') or file.filename.endswith('.edf')):
            file_path = os.path.join(study_dir, file.filename)
            file.save(file_path)
            file_paths.append(file_path)

    # Store study information
    uploaded_studies[study_id] = {
        "id": study_id,
        "path": study_dir,
        "file_count": len(file_paths)
    }

    return jsonify({
        "success": True,
        "study_id": study_id,
        "file_count": len(file_paths)
    })

@app.route('/api/study/<study_id>')
def get_study_data(study_id):
    # Check if the study exists in our uploaded studies
    study_info = uploaded_studies.get(study_id)
    if not study_info:
        return jsonify({"error": "Study not found"}), 404

    study_path = study_info["path"]

    # Get all signal files and annotation files
    signal_files = []
    annotation_file = None

    # Process DICOM files
    for file_path in Path(study_path).glob("*.dcm"):
        if "annotations" in file_path.name.lower():
            annotation_file = str(file_path)
        else:
            try:
                ds = pydicom.dcmread(file_path)
                sop_class_uid = ds.SOPClassUID
                signal_files.append({
                    "type": get_dicom_sop_title(sop_class_uid),
                    "path": str(file_path),
                    "channels": len(ds.WaveformSequence) if hasattr(ds, 'WaveformSequence') else 0
                })
            except Exception as e:
                # Skip files that can't be read as DICOM
                print(f"Error reading {file_path}: {str(e)}")

    # Process EDF files if pyedflib is available
    edf_annotation_files = []
    if EDF_SUPPORT:
        for file_path in Path(study_path).glob("*.edf"):
            try:
                with pyedflib.EdfReader(str(file_path)) as f:
                    signal_files.append({
                        "type": "EDF Signals",
                        "path": str(file_path),
                        "channels": f.signals_in_file
                    })
                    # Check if this EDF file has annotations
                    try:
                        onset, duration, description = f.readAnnotations()
                        if len(onset) > 0:
                            edf_annotation_files.append(file_path)
                    except:
                        pass  # No annotations in this file
            except Exception as e:
                print(f"Error reading EDF file {file_path}: {str(e)}")

    # Extract annotations from DICOM and EDF files
    annotations = []

    # DICOM annotations
    if annotation_file:
        try:
            ds = pydicom.dcmread(annotation_file)
            if hasattr(ds, 'ContentSequence'):
                for item in ds.ContentSequence:
                    if hasattr(item, 'ContentSequence') and len(item.ContentSequence) >= 2:
                        event_type = item.ContentSequence[0].ConceptNameCodeSequence[0].CodeValue
                        event_name = item.ContentSequence[0].ConceptNameCodeSequence[0].CodeMeaning
                        start_time = float(item.ContentSequence[1].MeasuredValueSequence[0].NumericValue)
                        duration = float(item.ContentSequence[2].MeasuredValueSequence[0].NumericValue)
                        annotations.append({
                            "type": event_type,
                            "type_name": event_name,
                            "start": start_time,
                            "duration": duration,
                            "end": start_time + duration
                        })
        except Exception as e:
            print(f"Error reading DICOM annotations: {str(e)}")

    # EDF+ annotations
    if EDF_SUPPORT:
        for edf_file in edf_annotation_files:
            try:
                with pyedflib.EdfReader(str(edf_file)) as f:
                    onset, duration, description = f.readAnnotations()
                    for i in range(len(onset)):
                        # Convert EDF+ annotation to our format
                        start_time = float(onset[i])
                        dur = float(duration[i]) if duration[i] > 0 else 0.0
                        desc = description[i].decode('utf-8') if isinstance(description[i], bytes) else str(description[i])

                        annotations.append({
                            "type": "EDF_ANNOTATION",
                            "type_name": desc,
                            "start": start_time,
                            "duration": dur,
                            "end": start_time + dur
                        })
            except Exception as e:
                print(f"Error reading EDF+ annotations from {edf_file}: {str(e)}")

    return jsonify({
        "signals": signal_files,
        "annotations": annotations
    })

@app.route('/api/signal/<signal_type>')
def get_signal_data(signal_type):
    file_path = request.args.get('path')
    if not file_path:
        return jsonify({"error": "No file path provided"}), 400

    # Get time window parameters
    start_time = float(request.args.get('start_time', 0))
    window_size = float(request.args.get('window_size', 30))

    # Detect file type
    file_type = detect_file_type(file_path)

    if file_type == 'dicom':
        return get_dicom_signal_data(file_path, start_time, window_size)
    elif file_type == 'edf' and EDF_SUPPORT:
        return get_edf_signal_data(file_path, start_time, window_size)
    else:
        return jsonify({"error": f"Unsupported file type: {file_type}"}), 400


def get_dicom_signal_data(file_path, start_time, window_size):
    """Extract signal data from DICOM file"""
    ds = pydicom.dcmread(file_path)

    waveform_data = []
    for waveform in ds.WaveformSequence:
        channel_name = waveform.ChannelDefinitionSequence[0].ChannelLabel
        sampling_rate = waveform.SamplingFrequency

        # Calculate sample indices based on time window
        start_sample = int(start_time * sampling_rate)
        end_sample = int((start_time + window_size) * sampling_rate)

        # Extract actual signal data
        if waveform.WaveformSampleInterpretation == 'SS':
            full_data = np.frombuffer(waveform.WaveformData, dtype=np.int16)
        elif waveform.WaveformSampleInterpretation == 'SL':
            full_data = np.frombuffer(waveform.WaveformData, dtype=np.int32)
        else:
            raise Exception(f"Can't read {waveform.WaveformSampleInterpretation} only SS and SL are supported")

        # Only extract the needed portion of data
        data = full_data[start_sample:min(end_sample, len(full_data))]

        waveform_data.append({
            "name": channel_name,
            "sampling_rate": sampling_rate,
            "data": data.tolist(),  # Only sending the requested window
            "total_samples": len(full_data)  # Include total sample count for UI reference
        })

    return jsonify(waveform_data)


def get_edf_signal_data(file_path, start_time, window_size):
    """Extract signal data from EDF file"""
    if not EDF_SUPPORT:
        return jsonify({"error": "EDF support not available"}), 400

    waveform_data = []

    with pyedflib.EdfReader(file_path) as f:
        n_signals = f.signals_in_file

        for i in range(n_signals):
            channel_name = f.getLabel(i)
            sampling_rate = f.getSampleFrequency(i)

            # Calculate sample indices based on time window
            start_sample = int(start_time * sampling_rate)
            end_sample = int((start_time + window_size) * sampling_rate)

            # Read the signal data for the time window
            total_samples = f.getNSamples()[i]
            end_sample = min(end_sample, total_samples)

            if start_sample < total_samples:
                # Read the data for this time window
                data = f.readSignal(i, start_sample, end_sample - start_sample)

                waveform_data.append({
                    "name": channel_name,
                    "sampling_rate": float(sampling_rate),
                    "data": data.tolist(),
                    "total_samples": int(total_samples)
                })

    return jsonify(waveform_data)

@app.route('/api/cleanup/<study_id>', methods=['DELETE'])
def cleanup_study(study_id):
    if study_id in uploaded_studies:
        study_path = uploaded_studies[study_id]["path"]
        try:
            shutil.rmtree(study_path)
            del uploaded_studies[study_id]
            return jsonify({"success": True})
        except Exception as e:
            return jsonify({"error": str(e)}), 500
    return jsonify({"error": "Study not found"}), 404

@app.route('/api/study/<study_id>/files', methods=['GET'])
def get_study_files(study_id):
    # Check if the study exists
    study_info = uploaded_studies.get(study_id)
    if not study_info:
        return jsonify({"error": "Study not found"}), 404

    study_path = study_info["path"]
    files = []

    # Get all files in the study directory
    all_files = list(Path(study_path).glob("*.dcm")) + list(Path(study_path).glob("*.edf"))

    for file_path in all_files:
        file_type = detect_file_type(file_path)
        patient_info = {}

        try:
            if file_type == 'dicom':
                ds = pydicom.dcmread(file_path)
                # Try to get patient information
                if hasattr(ds, 'PatientName'):
                    patient_info['name'] = str(ds.PatientName)
                if hasattr(ds, 'PatientID'):
                    patient_info['id'] = ds.PatientID
                if hasattr(ds, 'PatientBirthDate'):
                    patient_info['birthdate'] = ds.PatientBirthDate
                if hasattr(ds, 'PatientSex'):
                    patient_info['sex'] = ds.PatientSex

                # Get file type
                if hasattr(ds, 'SOPClassUID'):
                    file_type_name = get_dicom_sop_title(ds.SOPClassUID)
                elif "annotations" in file_path.name.lower():
                    file_type_name = "Annotations"
                else:
                    file_type_name = "DICOM"

            elif file_type == 'edf' and EDF_SUPPORT:
                with pyedflib.EdfReader(str(file_path)) as f:
                    # Try to get patient information from EDF header
                    patient_info['name'] = f.getPatientName() if hasattr(f, 'getPatientName') else ""
                    patient_info['id'] = f.getPatientCode() if hasattr(f, 'getPatientCode') else ""

                file_type_name = "EDF Signals"
            else:
                file_type_name = "Unknown"

            files.append({
                "filename": file_path.name,
                "path": str(file_path),
                "type": file_type_name,
                "size": file_path.stat().st_size,
                "patient_info": patient_info
            })
        except Exception as e:
            # If we can't read the file, just add basic file info
            files.append({
                "filename": file_path.name,
                "path": str(file_path),
                "type": "Unknown",
                "size": file_path.stat().st_size,
                "error": str(e)
            })

    return jsonify({
        "study_id": study_id,
        "files": files
    })

@app.route('/api/study/<study_id>/files/<filename>', methods=['DELETE'])
def delete_study_file(study_id, filename):
    # Check if the study exists
    study_info = uploaded_studies.get(study_id)
    if not study_info:
        return jsonify({"error": "Study not found"}), 404

    study_path = study_info["path"]
    file_path = os.path.join(study_path, filename)

    # Check if file exists
    if not os.path.exists(file_path):
        return jsonify({"error": "File not found"}), 404

    try:
        # Delete the file
        os.remove(file_path)
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/study/<study_id>/files', methods=['POST'])
def add_files_to_study(study_id):
    # Check if the study exists
    study_info = uploaded_studies.get(study_id)
    if not study_info:
        return jsonify({"error": "Study not found"}), 404

    if 'files[]' not in request.files:
        return jsonify({"error": "No files provided"}), 400

    files = request.files.getlist('files[]')
    if not files or files[0].filename == '':
        return jsonify({"error": "No files selected"}), 400

    study_path = study_info["path"]

    # Save uploaded files
    file_paths = []
    for file in files:
        if file.filename and (file.filename.endswith('.dcm') or file.filename.endswith('.edf')):
            file_path = os.path.join(study_path, file.filename)
            file.save(file_path)
            file_paths.append(file_path)

    # Update study information
    uploaded_studies[study_id]["file_count"] += len(file_paths)

    return jsonify({
        "success": True,
        "added_files": len(file_paths)
    })

@app.route('/standalone')
def standalone():
    return render_template('standalone.html')

@app.route('/api/open-local', methods=['POST'])
def open_local_files():
    file_paths = request.json.get('file_paths', [])

    if not file_paths:
        return jsonify({"error": "No files provided"}), 400

    # Create a new study ID and directory
    study_id = str(uuid.uuid4())
    study_dir = os.path.join(UPLOAD_FOLDER, study_id)
    os.makedirs(study_dir, exist_ok=True)

    # Copy files to our temporary directory
    copied_files = []
    for file_path in file_paths:
        if file_path.endswith('.dcm') or file_path.endswith('.edf'):
            filename = os.path.basename(file_path)
            dest_path = os.path.join(study_dir, filename)
            shutil.copy2(file_path, dest_path)
            copied_files.append(dest_path)

    # Store study information
    uploaded_studies[study_id] = {
        "id": study_id,
        "path": study_dir,
        "file_count": len(copied_files)
    }

    return jsonify({
        "success": True,
        "study_id": study_id,
        "file_count": len(copied_files)
    })

if __name__ == '__main__':
    app.run(debug=True, port=5002)