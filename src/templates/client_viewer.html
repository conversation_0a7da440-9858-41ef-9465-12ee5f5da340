<!DOCTYPE html>
<html>
<head>
    <title>Client-Side DICOM Viewer</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <!-- DICOM WebAssembly libraries -->
    <script src="https://unpkg.com/dcmjs/build/dcmjs.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { display: flex; height: 90vh; }
        .sidebar { width: 300px; border-right: 1px solid #ccc; padding: 10px; overflow: auto; }
        .main-view { flex: 1; padding: 10px; display: flex; flex-direction: column; }
        .hypnogram { height: 150px; margin-bottom: 20px; border: 1px solid #ddd; }
        .signals-container { flex: 1; overflow: auto; }

        /* Improved signal canvas layout */
        .signal-canvas {
            width: 100%;
            height: 140px;
            margin-bottom: 20px;
            border: 1px solid #eee;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        .signal-header {
            height: 30px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            padding: 0 10px;
            font-weight: bold;
        }
        .signal-plot-area {
            flex: 1;
            position: relative;
        }
        .channel-labels {
            position: absolute;
            left: 0;
            top: 0;
            width: 100px;
            height: 100%;
            z-index: 10;
            background-color: rgba(255, 255, 255, 0.7);
            border-right: 1px dashed #ccc;
            padding: 5px;
        }
        .channel-label {
            font-size: 10px;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .signal-x-axis { height: 20px; width: 100%; position: relative; }

        .controls { margin-bottom: 20px; }
        .annotation-list { overflow: auto; max-height: 300px; }
        .annotation-item { padding: 5px; cursor: pointer; border-bottom: 1px solid #eee; }
        .annotation-item:hover { background-color: #f5f5f5; }
        .window-size { margin: 10px 0; }
        .temp-message {
            position: fixed;
            top: 100px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 1000;
            transition: opacity 0.5s;
        }
        .event-controls {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .upload-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .upload-input {
            display: none;
        }
        .upload-btn {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .upload-btn:hover {
            background-color: #45a049;
        }
        .file-list {
            margin-top: 10px;
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 150px;
            overflow-y: auto;
        }
        .file-item {
            padding: 4px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-item-name {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .file-item-actions {
            margin-left: 10px;
        }
        .delete-file-btn {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            padding: 2px 5px;
            font-size: 10px;
        }
        .delete-file-btn:hover {
            background-color: #d32f2f;
        }
        .progress-container {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 4px;
            margin-top: 10px;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s;
        }

        /* Sidebar sections */
        .sidebar-section {
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        .sidebar-section h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .sidebar-section-toggle {
            cursor: pointer;
            font-size: 12px;
            color: #666;
        }

        /* Patient info section */
        .patient-info {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .patient-info-item {
            margin-bottom: 5px;
            font-size: 12px;
        }
        .patient-info-label {
            font-weight: bold;
            display: inline-block;
            width: 80px;
        }

        /* Study files section */
        .study-files {
            margin-bottom: 15px;
        }
        .add-files-btn {
            width: 100%;
            padding: 5px;
            margin-top: 10px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .add-files-btn:hover {
            background-color: #0b7dda;
        }

        /* Annotation tooltip */
        .annotation-tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 8px;
            border-radius: 3px;
            font-size: 11px;
            z-index: 100;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <h1>Client-Side DICOM Viewer</h1>
    <p class="description">All files are processed locally in your browser - nothing is uploaded to the server.</p>

    <div class="upload-section">
        <h3>Select DICOM and EDF Files</h3>
        <p>Select multiple DICOM (.dcm) or EDF (.edf) files for viewing. Include both signal and annotation files.</p>
        <input type="file" id="file-upload" class="upload-input" multiple accept=".dcm,.edf">
        <button class="upload-btn" id="select-files-btn">Select Files</button>
        <button class="upload-btn" id="process-btn" style="display:none;">Process Files</button>

        <div class="file-list" id="file-list" style="display:none;">
            <h4>Selected Files:</h4>
            <div id="selected-files"></div>
        </div>

        <div class="progress-container" id="progress-container">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
    </div>

    <div class="controls" style="display:none;" id="viewer-controls">
        <label>Window Size: <input type="number" id="window-size" value="30" min="5" max="120"> seconds</label>
        <button id="previous-window">Previous</button>
        <button id="next-window">Next</button>
    </div>

    <div class="event-controls" style="display:none;" id="event-controls">
        <label><input type="checkbox" id="show-respiratory" checked> Show Respiratory Events</label>
        <label><input type="checkbox" id="show-leg-movements" checked> Show Leg Movements</label>
        <label><input type="checkbox" id="show-arousals" checked> Show Arousals</label>
    </div>

    <div class="container" id="viewer-container" style="display:none;">
        <div class="sidebar">
            <!-- Patient Information Section -->
            <div class="sidebar-section" id="patient-info-section">
                <h3>
                    Patient Information
                    <span class="sidebar-section-toggle" id="toggle-patient-info">[-]</span>
                </h3>
                <div class="patient-info" id="patient-info">
                    <div class="patient-info-item">
                        <span class="patient-info-label">Name:</span>
                        <span id="patient-name">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">ID:</span>
                        <span id="patient-id">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">Birth Date:</span>
                        <span id="patient-birthdate">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">Sex:</span>
                        <span id="patient-sex">Unknown</span>
                    </div>
                </div>
            </div>

            <!-- Study Files Section -->
            <div class="sidebar-section" id="files-section">
                <h3>
                    Study Files
                    <span class="sidebar-section-toggle" id="toggle-files">[-]</span>
                </h3>
                <div class="study-files" id="study-files">
                    <div class="file-list" id="study-file-list">
                        <!-- Study files will be listed here -->
                    </div>
                    <button class="add-files-btn" id="add-files-btn">Add Files</button>
                    <input type="file" id="add-files-input" class="upload-input" multiple accept=".dcm">
                </div>
            </div>

            <!-- Annotations Section -->
            <div class="sidebar-section" id="annotations-section">
                <h3>
                    Annotations
                    <span class="sidebar-section-toggle" id="toggle-annotations">[-]</span>
                </h3>
                <div class="annotation-list" id="annotation-list"></div>
            </div>
        </div>

        <div class="main-view">
            <div class="hypnogram" id="hypnogram-container">
                <canvas id="hypnogram-canvas"></canvas>
            </div>

            <div class="signals-container" id="signals-container">
                <!-- Signal canvases will be added here -->
            </div>
        </div>
    </div>

    <!-- Client-side DICOM processing script -->
    <script src="/static/js/dicom-client.js"></script>
</body>
</html>
