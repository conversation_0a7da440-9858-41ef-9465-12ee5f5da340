<!DOCTYPE html>
<html>
<head>
    <title>PSG DICOM Viewer - Standalone</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { display: flex; height: 90vh; }
        .sidebar { width: 300px; border-right: 1px solid #ccc; padding: 10px; overflow: auto; }
        .main-view { flex: 1; padding: 10px; display: flex; flex-direction: column; }
        .hypnogram { height: 150px; margin-bottom: 20px; border: 1px solid #ddd; }
        .signals-container { flex: 1; overflow: auto; }

        /* Improved signal canvas layout */
        .signal-canvas {
            width: 100%;
            /* Height will be set dynamically based on number of channels */
            margin-bottom: 30px; /* Increased margin between signal groups */
            border: 1px solid #ddd;
            border-radius: 4px;
            position: relative;
            display: flex;
            flex-direction: column;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Add subtle shadow for better separation */
            overflow: visible; /* Allow content to be visible outside the container */
        }
        .signal-header {
            height: 30px;
            background-color: #f0f7ff; /* Light blue background */
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            padding: 0 10px;
            font-weight: bold;
            border-left: 4px solid #2196F3; /* Blue accent for headers */
            font-size: 14px; /* Larger font for better readability */
            letter-spacing: 0.5px; /* Slightly spaced letters */
        }
        .signal-plot-area {
            flex: 1;
            position: relative;
            overflow: hidden; /* Keep the plot area content contained */
            margin-bottom: 0; /* Ensure no margin at the bottom */
            padding-bottom: 0; /* Ensure no padding at the bottom */
        }
        .channel-labels {
            position: absolute;
            left: 0;
            top: 0;
            width: 100px; /* Wider to accommodate units and ranges */
            height: 100%;
            z-index: 10;
            background-color: rgba(255, 255, 255, 0.95); /* More opaque background */
            border-right: 1px solid #ddd;
            padding: 5px;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 4px rgba(0,0,0,0.05); /* Subtle shadow for depth */
            overflow: visible; /* Allow content to overflow if needed */
        }
        .channel-label {
            font-size: 10px;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            background-color: rgba(245, 245, 245, 0.8); /* Light background for better readability */
            padding: 2px 4px;
            border-radius: 2px;
            border-left: 3px solid #2196F3; /* Blue accent for labels */
        }
        .channel-units {
            font-size: 8px;
            color: #666;
            display: block;
            margin-top: 2px;
        }
        .channel-range {
            font-size: 8px;
            color: #666;
            display: block;
            margin-top: 1px;
        }
        .signal-x-axis {
            height: 30px;
            width: 100%; /* Full width of the plot area */
            position: absolute;
            bottom: 0; /* Position at the bottom of the plot area */
            left: 0;
            background-color: #f9f9f9;
            border-top: 1px solid #ddd; /* Add top border for separation */
            padding-bottom: 5px; /* Add padding at the bottom for the label */
            z-index: 5; /* Ensure axis is above other elements */
        }
        .time-tick {
            position: absolute;
            font-size: 9px;
            color: #666;
            text-align: center;
            width: 40px;
            margin-left: -20px;
        }
        .time-tick-mark {
            position: absolute;
            height: 5px;
            width: 1px;
            background-color: #999;
        }

        .controls { margin-bottom: 20px; }
        .annotation-list { overflow: auto; max-height: 300px; }
        .annotation-item { padding: 5px; cursor: pointer; border-bottom: 1px solid #eee; }
        .annotation-item:hover { background-color: #f5f5f5; }
        .window-size { margin: 10px 0; }
        .temp-message {
            position: fixed;
            top: 100px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 1000;
            transition: opacity 0.5s;
        }
        .event-controls {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }

        .scaling-toggle-btn {
            margin-left: 20px;
            padding: 6px 12px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .scaling-toggle-btn:hover {
            background-color: #45a049;
        }

        .scaling-toggle-btn:active {
            background-color: #3d8b40;
        }
        .file-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .file-btn {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .file-btn:hover {
            background-color: #45a049;
        }
        .file-list {
            margin-top: 10px;
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 150px;
            overflow-y: auto;
        }
        .file-item {
            padding: 4px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-item-name {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .file-item-actions {
            margin-left: 10px;
        }
        .delete-file-btn {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            padding: 2px 5px;
            font-size: 10px;
        }
        .delete-file-btn:hover {
            background-color: #d32f2f;
        }
        .progress-container {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 4px;
            margin-top: 10px;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s;
        }

        /* Sidebar sections */
        .sidebar-section {
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        .sidebar-section h3 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .sidebar-section-toggle {
            cursor: pointer;
            font-size: 12px;
            color: #666;
        }

        /* Patient info section */
        .patient-info {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .patient-info-item {
            margin-bottom: 5px;
            font-size: 12px;
        }
        .patient-info-label {
            font-weight: bold;
            display: inline-block;
            width: 80px;
        }

        /* Study files section */
        .study-files {
            margin-bottom: 15px;
        }
        .add-files-btn {
            width: 100%;
            padding: 5px;
            margin-top: 10px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .add-files-btn:hover {
            background-color: #0b7dda;
        }

        /* Annotation tooltip */
        .annotation-tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 8px;
            border-radius: 3px;
            font-size: 11px;
            z-index: 100;
            pointer-events: none;
        }

        .signal-tooltip {
            position: fixed;
            background-color: rgba(0, 0, 0, 0.85);
            color: white;
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
            z-index: 1000;
            pointer-events: none;
            border-left: 3px solid #2196F3;
            box-shadow: 0 3px 8px rgba(0,0,0,0.4);
            max-width: 200px;
            transform: translate3d(0, 0, 0); /* Force hardware acceleration for smoother movement */
            transition: opacity 0.1s ease-out; /* Smooth fade in/out */
        }
    </style>
</head>
<body>
    <h1>PSG DICOM Viewer - Standalone</h1>

    <div class="file-section">
        <h3>Open DICOM and EDF Files</h3>
        <p>Select a folder containing DICOM (.dcm) or EDF (.edf) files for viewing. Include both signal and annotation files.</p>
        <button class="file-btn" id="select-folder-btn">Select Folder</button>
        <button class="file-btn" id="process-btn" style="display:none;">Process Files</button>
        <button class="file-btn" id="clear-study-btn" style="background-color: #f44336; display:none;">Clear Current Study</button>

        <div class="file-list" id="file-list" style="display:none;">
            <h4>Selected Files:</h4>
            <div id="selected-files"></div>
        </div>

        <div class="progress-container" id="progress-container">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
    </div>

    <div class="controls" style="display:none;" id="viewer-controls">
        <label>Window Size: <input type="number" id="window-size" value="30" min="5" max="120"> seconds</label>
        <button id="previous-window">Previous</button>
        <button id="next-window">Next</button>
    </div>

    <div class="event-controls" style="display:none;" id="event-controls">
        <label><input type="checkbox" id="show-respiratory" checked> Show Respiratory Events</label>
        <label><input type="checkbox" id="show-leg-movements" checked> Show Leg Movements</label>
        <label><input type="checkbox" id="show-arousals" checked> Show Arousals</label>
        <button id="dynamic-scaling-toggle" class="scaling-toggle-btn" title="Currently using dynamic scaling (min/max). Click to use AASM default scaling.">Switch to AASM Scaling</button>
    </div>

    <div class="container" id="viewer-container" style="display:none;">
        <div class="sidebar">
            <!-- Patient Information Section -->
            <div class="sidebar-section" id="patient-info-section">
                <h3>
                    Patient Information
                    <span class="sidebar-section-toggle" id="toggle-patient-info">[-]</span>
                </h3>
                <div class="patient-info" id="patient-info">
                    <div class="patient-info-item">
                        <span class="patient-info-label">Name:</span>
                        <span id="patient-name">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">ID:</span>
                        <span id="patient-id">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">Birth Date:</span>
                        <span id="patient-birthdate">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">Sex:</span>
                        <span id="patient-sex">Unknown</span>
                    </div>
                </div>
            </div>

            <!-- Study Files Section -->
            <div class="sidebar-section" id="files-section">
                <h3>
                    Study Files
                    <span class="sidebar-section-toggle" id="toggle-files">[-]</span>
                </h3>
                <div class="study-files" id="study-files">
                    <div class="file-list" id="study-file-list">
                        <!-- Study files will be listed here -->
                    </div>
                    <button class="add-files-btn" id="add-files-btn">Add Files</button>
                </div>
            </div>

            <!-- Annotations Section -->
            <div class="sidebar-section" id="annotations-section">
                <h3>
                    Annotations
                    <span class="sidebar-section-toggle" id="toggle-annotations">[-]</span>
                </h3>
                <div class="annotation-list" id="annotation-list"></div>
            </div>
        </div>

        <div class="main-view">
            <div class="hypnogram" id="hypnogram-container">
                <canvas id="hypnogram-canvas"></canvas>
            </div>

            <div class="signals-container" id="signals-container">
                <!-- Signal canvases will be added here -->
            </div>
        </div>
    </div>

    <script>
        // This script will be replaced with the actual implementation
        // It will handle communication with the Python backend
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the standalone viewer
            document.getElementById('select-folder-btn').addEventListener('click', function() {
                // This will be handled by the Python backend
                window.pywebview.api.select_folder().then(function(result) {
                    if (result.success) {
                        // Display selected files
                        const fileListEl = document.getElementById('file-list');
                        const selectedFilesEl = document.getElementById('selected-files');
                        selectedFilesEl.innerHTML = '';
                        
                        if (result.files.length > 0) {
                            fileListEl.style.display = 'block';
                            document.getElementById('process-btn').style.display = 'inline-block';
                            
                            result.files.forEach(file => {
                                const fileItem = document.createElement('div');
                                fileItem.className = 'file-item';
                                fileItem.textContent = file;
                                selectedFilesEl.appendChild(fileItem);
                            });
                        }
                    } else {
                        showTempMessage('Error: ' + result.error);
                    }
                });
            });
            
            document.getElementById('process-btn').addEventListener('click', function() {
                // Process the selected files
                window.pywebview.api.process_files().then(function(result) {
                    if (result.success) {
                        // Show viewer controls
                        document.getElementById('viewer-controls').style.display = 'block';
                        document.getElementById('event-controls').style.display = 'block';
                        document.getElementById('viewer-container').style.display = 'flex';
                        document.getElementById('clear-study-btn').style.display = 'inline-block';
                        document.getElementById('process-btn').style.display = 'none';
                        
                        // Load the study data
                        loadStudyData();
                    } else {
                        showTempMessage('Error: ' + result.error);
                    }
                });
            });
            
            document.getElementById('clear-study-btn').addEventListener('click', function() {
                if (confirm('Are you sure you want to clear the current study?')) {
                    window.pywebview.api.clear_study().then(function(result) {
                        if (result.success) {
                            // Reset UI
                            document.getElementById('viewer-controls').style.display = 'none';
                            document.getElementById('event-controls').style.display = 'none';
                            document.getElementById('viewer-container').style.display = 'none';
                            document.getElementById('clear-study-btn').style.display = 'none';
                            document.getElementById('file-list').style.display = 'none';
                            
                            showTempMessage('Study cleared successfully');
                        } else {
                            showTempMessage('Error: ' + result.error);
                        }
                    });
                }
            });
            
            document.getElementById('add-files-btn').addEventListener('click', function() {
                window.pywebview.api.add_files().then(function(result) {
                    if (result.success) {
                        // Reload the study data
                        loadStudyData();
                        showTempMessage('Files added successfully');
                    } else {
                        showTempMessage('Error: ' + result.error);
                    }
                });
            });
            
            // Function to load study data
            function loadStudyData() {
                window.pywebview.api.get_study_data().then(function(result) {
                    if (result.success) {
                        // Update the UI with the study data
                        updateStudyUI(result.data);
                    } else {
                        showTempMessage('Error: ' + result.error);
                    }
                });
            }
            
            // Function to update the UI with study data
            function updateStudyUI(data) {
                // Update patient info
                if (data.patient_info) {
                    document.getElementById('patient-name').textContent = data.patient_info.name || 'Unknown';
                    document.getElementById('patient-id').textContent = data.patient_info.id || 'Unknown';
                    document.getElementById('patient-birthdate').textContent = data.patient_info.birthdate || 'Unknown';
                    document.getElementById('patient-sex').textContent = data.patient_info.sex || 'Unknown';
                }
                
                // Update file list
                const fileList = document.getElementById('study-file-list');
                fileList.innerHTML = '';
                
                if (data.files && data.files.length > 0) {
                    data.files.forEach(file => {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'file-item';
                        fileItem.innerHTML = `
                            <div class="file-item-name" title="${file.filename}">${file.filename}</div>
                            <div class="file-item-actions">
                                <button class="delete-file-btn" data-filename="${file.filename}">Delete</button>
                            </div>
                        `;
                        fileList.appendChild(fileItem);
                    });
                    
                    // Add event listeners for delete buttons
                    document.querySelectorAll('.delete-file-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const filename = this.getAttribute('data-filename');
                            if (confirm(`Are you sure you want to delete ${filename}?`)) {
                                window.pywebview.api.delete_file(filename).then(function(result) {
                                    if (result.success) {
                                        loadStudyData();
                                        showTempMessage(`Deleted ${filename}`);
                                    } else {
                                        showTempMessage('Error: ' + result.error);
                                    }
                                });
                            }
                        });
                    });
                } else {
                    fileList.innerHTML = '<div class="file-item">No files found</div>';
                }
                
                // Load signals and annotations
                // This would be implemented in the full version
            }
            
            // Function to show temporary message
            function showTempMessage(message) {
                // Remove any existing message
                const existingMsg = document.querySelector('.temp-message');
                if (existingMsg) {
                    existingMsg.remove();
                }
                
                // Create new message
                const msgDiv = document.createElement('div');
                msgDiv.className = 'temp-message';
                msgDiv.textContent = message;
                document.body.appendChild(msgDiv);
                
                // Remove after 2 seconds
                setTimeout(() => {
                    msgDiv.style.opacity = '0';
                    setTimeout(() => msgDiv.remove(), 500);
                }, 2000);
            }
            
            // Check if we have a study already loaded
            window.pywebview.api.check_study_loaded().then(function(result) {
                if (result.success && result.loaded) {
                    // Show viewer controls
                    document.getElementById('viewer-controls').style.display = 'block';
                    document.getElementById('event-controls').style.display = 'block';
                    document.getElementById('viewer-container').style.display = 'flex';
                    document.getElementById('clear-study-btn').style.display = 'inline-block';
                    
                    // Load the study data
                    loadStudyData();
                }
            });
        });
    </script>
</body>
</html>
