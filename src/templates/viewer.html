<!DOCTYPE html>
<html>
<head>
    <title>PSG DICOM Viewer</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <h1>PSG DICOM Viewer</h1>

    <div class="upload-section">
        <h3>Upload DICOM and EDF Files</h3>
        <p>Select multiple DICOM (.dcm) or EDF (.edf) files for viewing. Include both signal and annotation files.</p>
        <input type="file" id="file-upload" class="upload-input" multiple accept=".dcm,.edf">
        <button class="upload-btn" id="select-files-btn">Select Files</button>
        <button class="upload-btn" id="upload-btn" style="display:none;">Upload Files</button>
        <button class="upload-btn" id="clear-study-btn" style="background-color: #f44336; display:none;">Clear Current Study</button>

        <div class="file-list" id="file-list" style="display:none;">
            <h4>Selected Files:</h4>
            <div id="selected-files"></div>
        </div>

        <div class="progress-container" id="progress-container">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
    </div>

    <div class="controls" style="display:none;" id="viewer-controls">
        <label>Window Size: <input type="number" id="window-size" value="30" min="5" max="120"> seconds</label>
        <button id="previous-window">Previous</button>
        <button id="next-window">Next</button>

        <div class="time-navigation">
            <label for="start-time-input">Start Time:</label>
            <input type="number" id="start-time-input" value="0" min="0" step="1"> seconds
            <button id="goto-time-btn">Go</button>
            <button id="prev-5s-btn">-5s</button>
            <button id="next-5s-btn">+5s</button>
        </div>
    </div>

    <div class="event-controls" style="display:none;" id="event-controls">
        <label><input type="checkbox" id="show-respiratory" checked> Show Respiratory Events</label>
        <label><input type="checkbox" id="show-leg-movements" checked> Show Leg Movements</label>
        <label><input type="checkbox" id="show-arousals" checked> Show Arousals</label>
        <button id="dynamic-scaling-toggle" class="scaling-toggle-btn" title="Currently using dynamic scaling (min/max). Click to use AASM default scaling.">Switch to AASM Scaling</button>
    </div>

    <div class="container" id="viewer-container" style="display:none;">
        <div class="sidebar">
            <!-- Patient Information Section -->
            <div class="sidebar-section" id="patient-info-section">
                <h3>
                    Patient Information
                    <span class="sidebar-section-toggle" id="toggle-patient-info">[-]</span>
                </h3>
                <div class="patient-info" id="patient-info">
                    <div class="patient-info-item">
                        <span class="patient-info-label">Name:</span>
                        <span id="patient-name">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">ID:</span>
                        <span id="patient-id">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">Birth Date:</span>
                        <span id="patient-birthdate">Unknown</span>
                    </div>
                    <div class="patient-info-item">
                        <span class="patient-info-label">Sex:</span>
                        <span id="patient-sex">Unknown</span>
                    </div>
                </div>
            </div>

            <!-- Study Files Section -->
            <div class="sidebar-section" id="files-section">
                <h3>
                    Study Files
                    <span class="sidebar-section-toggle" id="toggle-files">[-]</span>
                </h3>
                <div class="study-files" id="study-files">
                    <div class="file-list" id="study-file-list">
                        <!-- Study files will be listed here -->
                    </div>
                    <button class="add-files-btn" id="add-files-btn">Add Files</button>
                    <input type="file" id="add-files-input" class="upload-input" multiple accept=".dcm">
                </div>
            </div>

            <!-- Annotations Section -->
            <div class="sidebar-section" id="annotations-section">
                <h3>
                    Annotations
                    <span class="sidebar-section-toggle" id="toggle-annotations">[-]</span>
                </h3>
                <div class="annotation-list" id="annotation-list"></div>
            </div>

            <!-- Event Summary Section -->
            <div class="sidebar-section" id="events-summary-section">
                <h3>
                    Event Summary
                    <span class="sidebar-section-toggle" id="toggle-events-summary">[-]</span>
                </h3>
                <div class="events-summary" id="events-summary">
                    <!-- Event counts will be populated here -->
                </div>
            </div>
        </div>

        <div class="main-view">
            <div class="hypnogram" id="hypnogram-container">
                <canvas id="hypnogram-canvas"></canvas>
            </div>

            <div class="signals-container" id="signals-container">
                <!-- Signal canvases will be added here -->
            </div>
        </div>
    </div>

    <!-- Load modular JavaScript files -->
    <script type="module" src="/static/js/main.js"></script>
</body>
</html>
