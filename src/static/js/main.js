import <PERSON>com<PERSON>oader from './dicom-loader.js';
import SignalRenderer from './signal-renderer.js';
import AnnotationManager from './annotation-manager.js';
import UIController from './ui-controller.js';

// Main application class
class DicomViewerApp {
    constructor() {
        // Initialize components
        this.dicomLoader = new DicomLoader();
        this.signalRenderer = new SignalRenderer();
        this.annotationManager = new AnnotationManager();
        this.uiController = new UIController();

        // Application state
        this.currentStudy = null;
        this.currentSignals = [];
        this.annotations = [];
        this.signalData = {};
    }

    // Initialize the application
    async initialize() {
        // Set up event handlers for UI controller
        this.setupEventHandlers();
        
        // Initialize UI
        this.uiController.initializeUI();
        
        // Check for stored study
        await this.initializeWithStoredStudy();
    }

    // Set up event handlers
    setupEventHandlers() {
        // UI Controller events
        this.uiController.onUploadFiles = this.handleUploadFiles.bind(this);
        this.uiController.onLoadStudy = this.loadStudyData.bind(this);
        this.uiController.onCleanupStudy = this.cleanupStudy.bind(this);
        this.uiController.onNavigate = this.updateView.bind(this);
        this.uiController.onEventFilterChange = this.handleEventFilterChange.bind(this);
        this.uiController.onDynamicScalingToggle = this.handleDynamicScalingToggle.bind(this);
        this.uiController.onAddFiles = this.handleAddFiles.bind(this);
        this.uiController.onDeleteFile = this.handleDeleteFile.bind(this);
        this.uiController.onRefreshView = this.refreshView.bind(this);

        // Annotation Manager events
        this.annotationManager.onAnnotationClick = this.navigateToTime.bind(this);
        this.annotationManager.onHypnogramClick = this.navigateToTime.bind(this);

        // Signal Renderer events
        this.signalRenderer.onAnnotationClick = this.navigateToTime.bind(this);
        this.signalRenderer.onAnnotationListHighlight = this.highlightAnnotationInList.bind(this);
        this.signalRenderer.onSignalReorder = this.handleSignalReorder.bind(this);
    }

    // Initialize with stored study ID
    async initializeWithStoredStudy() {
        const storedStudyId = localStorage.getItem('currentStudyId');
        
        if (storedStudyId) {
            try {
                // Check if the study still exists on the server
                const response = await fetch(`/api/study/${storedStudyId}/files`);

                if (response.ok) {
                    // Study exists, load it
                    this.uiController.currentStudyId = storedStudyId;

                    // Show viewer controls
                    document.getElementById('viewer-controls').style.display = 'block';
                    document.getElementById('event-controls').style.display = 'block';
                    document.getElementById('viewer-container').style.display = 'flex';
                    document.getElementById('clear-study-btn').style.display = 'inline-block';

                    // Load the study data
                    await this.loadStudyData(storedStudyId);
                    this.uiController.showTempMessage('Previous study loaded successfully');
                } else {
                    // Study doesn't exist anymore, clear the stored ID
                    localStorage.removeItem('currentStudyId');
                }
            } catch (error) {
                console.error('Error loading stored study:', error);
                localStorage.removeItem('currentStudyId');
            }
        }
    }

    // Handle file upload
    async handleUploadFiles(files, progressCallback) {
        // Create FormData with files
        const formData = new FormData();
        files.forEach(file => {
            formData.append('files[]', file);
        });

        try {
            // Upload files
            const response = await fetch('/api/upload-files', {
                method: 'POST',
                body: formData,
                onUploadProgress: progressCallback
            });

            return await response.json();
        } catch (error) {
            console.error('Upload error:', error);
            throw error;
        }
    }

    // Load study data
    async loadStudyData(studyId) {
        this.uiController.showTempMessage('Loading study data...');

        try {
            // Load study data
            const studyData = await this.dicomLoader.loadStudyData(studyId);
            this.currentStudy = studyData;
            this.annotations = studyData.annotations;
            this.currentSignals = studyData.signals;

            // Update annotation manager
            this.annotationManager.setAnnotations(this.annotations);
            
            // Initialize signal containers
            this.signalRenderer.initializeSignalContainers(this.currentSignals, 'signals-container');

            // Populate annotation list and events summary
            this.annotationManager.populateAnnotations(this.annotations, 'annotation-list');
            this.annotationManager.populateEventsSummary(this.annotations, 'events-summary');

            // Draw hypnogram
            this.annotationManager.drawHypnogram(this.annotations, 'hypnogram-canvas');

            // Load study files and patient information
            await this.loadStudyFiles(studyId);

            // Initialize view
            this.uiController.currentStartTime = 0;
            this.annotationManager.setTimeWindow(0, this.uiController.windowSizeInSeconds);
            await this.updateView(0, this.uiController.windowSizeInSeconds);

            this.uiController.showTempMessage('Study loaded successfully');
        } catch (error) {
            console.error('Error loading study:', error);
            this.uiController.showTempMessage('Error loading study: ' + error.message);
        }
    }

    // Load study files and patient information
    async loadStudyFiles(studyId) {
        try {
            const data = await this.dicomLoader.loadStudyFiles(studyId);
            const files = data.files;

            // Update file list
            this.uiController.updateStudyFileList(files, studyId);

            // Update patient information if available
            const fileWithPatientInfo = files.find(file =>
                file.patient_info && Object.keys(file.patient_info).length > 0
            );
            
            if (fileWithPatientInfo) {
                this.uiController.updatePatientInfo(fileWithPatientInfo.patient_info);
            }
        } catch (error) {
            console.error('Error loading study files:', error);
            this.uiController.showTempMessage('Error loading study files: ' + error.message);
        }
    }

    // Update view with current time window
    async updateView(startTime, windowSize) {
        // Update time window in annotation manager
        this.annotationManager.setTimeWindow(startTime, windowSize);
        
        // Calculate end time of current window
        const timeEnd = startTime + windowSize;

        // Load data for this window
        try {
            this.uiController.showTempMessage(`Loading data for current window...`);
            this.signalData = await this.dicomLoader.loadSignalWindow(this.currentSignals, startTime, timeEnd);
            
            // Update signal renderer with new data
            this.signalRenderer.setSignalData(this.signalData);
            
            // For each signal type, draw the current window
            for (const signalType in this.signalData) {
                this.signalRenderer.drawSignal(signalType, startTime, timeEnd, this.annotations);
            }

            // Highlight the current window in the hypnogram
            this.annotationManager.highlightHypnogramWindow('hypnogram-canvas');

            // Highlight visible annotations
            this.annotationManager.highlightVisibleAnnotations('annotation-list');

            // Show current time window info
            const startMinutes = Math.floor(startTime / 60);
            const startSeconds = Math.floor(startTime % 60);
            const endMinutes = Math.floor(timeEnd / 60);
            const endSeconds = Math.floor(timeEnd % 60);

            this.uiController.showTempMessage(`Viewing window: ${startMinutes}:${startSeconds.toString().padStart(2, '0')} - ${endMinutes}:${endSeconds.toString().padStart(2, '0')}`);
        } catch (error) {
            console.error('Error updating view:', error);
            this.uiController.showTempMessage('Error updating view: ' + error.message);
        }
    }

    // Navigate to specific time
    navigateToTime(timeInSeconds, leftAligned = false) {
        this.uiController.navigateToTime(timeInSeconds, leftAligned);
    }

    // Highlight annotation in the list
    highlightAnnotationInList(annotationStartTime) {
        this.annotationManager.highlightAnnotationInList(annotationStartTime);
    }

    // Handle signal reorder
    handleSignalReorder() {
        // Get the current order of signal containers
        const container = document.getElementById('signals-container');
        const signalCanvases = container.querySelectorAll('.signal-canvas');
        const newOrder = [];

        signalCanvases.forEach(canvas => {
            const signalType = canvas.dataset.signalType;
            const signalFile = this.currentSignals.find(s => s.type === signalType);
            if (signalFile) {
                newOrder.push(signalFile);
            }
        });

        // Update the current signals order
        this.currentSignals = newOrder;

        // Redraw all signals with the new order
        this.updateView(this.uiController.currentStartTime, this.uiController.windowSizeInSeconds);
    }

    // Handle event filter change
    handleEventFilterChange() {
        // Update annotation list visibility
        this.annotationManager.updateAnnotationListVisibility('annotation-list');

        // Redraw hypnogram
        this.annotationManager.drawHypnogram(this.annotations, 'hypnogram-canvas');

        // Update view
        this.updateView(this.uiController.currentStartTime, this.uiController.windowSizeInSeconds);
    }

    // Handle dynamic scaling toggle
    handleDynamicScalingToggle() {
        // Toggle the scaling mode in the signal renderer
        this.signalRenderer.toggleDynamicScaling();

        // Redraw all signals with the new scaling mode
        this.updateView(this.uiController.currentStartTime, this.uiController.windowSizeInSeconds);
    }

    // Handle adding files to study
    async handleAddFiles(studyId, files) {
        return await this.dicomLoader.addFilesToStudy(studyId, files);
    }

    // Handle deleting a file from study
    async handleDeleteFile(studyId, filename) {
        try {
            const result = await this.dicomLoader.deleteStudyFile(studyId, filename);
            this.uiController.showTempMessage(`Deleted ${filename}`);
            
            // Reload study data
            await this.loadStudyData(studyId);
            return result;
        } catch (error) {
            console.error('Error deleting file:', error);
            this.uiController.showTempMessage('Error deleting file: ' + error.message);
            throw error;
        }
    }

    // Clean up study
    async cleanupStudy(studyId) {
        await this.dicomLoader.cleanupStudy(studyId);
    }

    // Refresh current view
    async refreshView() {
        await this.updateView(this.uiController.currentStartTime, this.uiController.windowSizeInSeconds);
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new DicomViewerApp();
    app.initialize();
});
