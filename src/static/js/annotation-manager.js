// Annotation Manager - Handles annotation data and rendering
class AnnotationManager {
    constructor() {
        this.annotations = [];
        this.currentStartTime = 0;
        this.windowSizeInSeconds = 30;
    }

    // Set annotations
    setAnnotations(annotations) {
        this.annotations = annotations;
    }

    // Draw hypnogram
    drawHypnogram(annotations, canvasId) {
        const canvas = document.getElementById(canvasId);
        canvas.width = canvas.parentElement.clientWidth;
        canvas.height = canvas.parentElement.clientHeight;

        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Find sleep stage annotations
        const sleepStages = annotations.filter(a =>
            a.type === '2:23672' || // Wake
            a.type === '130834' ||  // N1
            a.type === '130835' ||  // N2
            a.type === '130836' ||  // N3
            a.type === '2:23680'    // REM
        );

        if (sleepStages.length === 0) return;

        // Find start and end times
        const startTime = Math.min(...annotations.map(a => a.start));
        const endTime = Math.max(...annotations.map(a => a.start + a.duration));
        const totalDuration = endTime - startTime;

        // Map sleep stages to y-positions
        const stageMap = {
            '2:23680': 5, // REM
            '2:23672': 4, // Wake
            '130834': 3,  // N1
            '130835': 2,  // N2
            '130836': 1,  // N3
        };

        // Draw sleep stages
        let lastX = 0;
        let lastY = canvas.height - (2 * 20); // Default to wake position
        let lastStageType = null;

        sleepStages.forEach(stage => {
            const x = ((stage.start - startTime) / totalDuration) * canvas.width;
            const stageLevel = stageMap[stage.type] || 2; // Default if unknown
            const y = canvas.height - (stageLevel * 20);

            // Draw vertical transition line (always black)
            ctx.beginPath();
            ctx.moveTo(x, lastY);
            ctx.lineTo(x, y);
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw horizontal plateau line with appropriate color
            if (lastX > 0) {
                ctx.beginPath();
                ctx.moveTo(lastX, lastY);
                ctx.lineTo(x, lastY);

                // Color REM plateaus purple, others black
                if (lastStageType === '2:23680') { // REM
                    ctx.strokeStyle = 'purple';
                } else {
                    ctx.strokeStyle = 'black';
                }
                ctx.lineWidth = 2;
                ctx.stroke();
            }

            lastX = x;
            lastY = y;
            lastStageType = stage.type;
        });

        // Draw the final segment to the end of canvas
        ctx.beginPath();
        ctx.moveTo(lastX, lastY);
        ctx.lineTo(canvas.width, lastY);
        if (lastStageType === '2:23680') { // REM
            ctx.strokeStyle = 'red';
        } else {
            ctx.strokeStyle = 'black';
        }
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw labels
        ctx.font = '12px Arial';
        ctx.fillText('REM', 5, canvas.height - 90);
        ctx.fillText('Wake', 5, canvas.height - 70);
        ctx.fillText('N1', 5, canvas.height - 50);
        ctx.fillText('N2', 5, canvas.height - 30);
        ctx.fillText('N3', 5, canvas.height - 10);

        // Draw additional events by category
        const respiratoryEvents = annotations.filter(a => 
            this.getEventTypeInfo(a).category === 'respiratory' && 
            this.getEventTypeInfo(a).visible
        );
        this.drawEventsOnHypnogram(ctx, respiratoryEvents, startTime, totalDuration, canvas.width, canvas.height, 'red');

        const limbEvents = annotations.filter(a => 
            this.getEventTypeInfo(a).category === 'limb' && 
            this.getEventTypeInfo(a).visible
        );
        this.drawEventsOnHypnogram(ctx, limbEvents, startTime, totalDuration, canvas.width, canvas.height, 'green');

        const arousalEvents = annotations.filter(a => 
            this.getEventTypeInfo(a).category === 'arousal' && 
            this.getEventTypeInfo(a).visible
        );
        this.drawEventsOnHypnogram(ctx, arousalEvents, startTime, totalDuration, canvas.width, canvas.height, 'orange');

        // Add click handler for navigation
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const clickedTime = startTime + (x / canvas.width) * totalDuration;
            if (this.onHypnogramClick) {
                this.onHypnogramClick(clickedTime);
            }
        });

        // Store these values for highlighting the current window
        this.hypnogramStartTime = startTime;
        this.hypnogramTotalDuration = totalDuration;
    }

    // Draw events on the hypnogram
    drawEventsOnHypnogram(ctx, events, startTime, totalDuration, canvasWidth, canvasHeight, color) {
        events.forEach(event => {
            const x = ((event.start - startTime) / totalDuration) * canvasWidth;
            const width = (event.duration / totalDuration) * canvasWidth;

            // Draw event marker
            ctx.fillStyle = color;
            ctx.fillRect(x, 0, width, 10);

            // Draw vertical line
            ctx.strokeStyle = color;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, canvasHeight);
            ctx.stroke();
        });
    }

    // Highlight current window in hypnogram
    highlightHypnogramWindow(canvasId) {
        const canvas = document.getElementById(canvasId);
        const ctx = canvas.getContext('2d');

        // First redraw the hypnogram
        this.drawHypnogram(this.annotations, canvasId);

        // Calculate window position
        const windowStartX = ((this.currentStartTime - this.hypnogramStartTime) / this.hypnogramTotalDuration) * canvas.width;
        const windowEndX = ((this.currentStartTime + this.windowSizeInSeconds - this.hypnogramStartTime) / this.hypnogramTotalDuration) * canvas.width;

        // Draw window highlight
        ctx.fillStyle = 'rgba(173, 216, 230, 0.3)';  // Light blue with 30% opacity
        ctx.fillRect(windowStartX, 0, windowEndX - windowStartX, canvas.height);

        // Draw window borders
        ctx.strokeStyle = 'rgba(70, 130, 180, 0.8)';  // Steel blue with 80% opacity
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(windowStartX, 0);
        ctx.lineTo(windowStartX, canvas.height);
        ctx.moveTo(windowEndX, 0);
        ctx.lineTo(windowEndX, canvas.height);
        ctx.stroke();
    }

    // Populate annotation list
    populateAnnotations(annotations, listId) {
        const list = document.getElementById(listId);
        list.innerHTML = '';

        annotations.sort((a, b) => a.start - b.start);

        annotations.forEach((annotation, index) => {
            const item = document.createElement('div');
            item.className = 'annotation-item';
            item.dataset.annotationIndex = index; // Add index for easy lookup
            const minutes = Math.floor(annotation.start / 60);
            const seconds = Math.floor(annotation.start % 60);
            const durationSeconds = Math.round(annotation.duration);

            // Get event type information
            const eventInfo = this.getEventTypeInfo(annotation);

            // Use type_name for display if available, otherwise use displayName from eventInfo
            // Include duration in the display text
            item.textContent = `[${minutes}:${seconds.toString().padStart(2, '0')}] ${eventInfo.displayName} (${durationSeconds}s)`;

            // Apply border color based on event type
            item.style.borderLeft = `4px solid ${eventInfo.borderColor}`;

            // Add data attributes for filtering
            item.dataset.category = eventInfo.category;
            item.dataset.eventType = annotation.type;
            item.dataset.startTime = annotation.start;
            item.dataset.displayName = eventInfo.displayName;

            // Add click handler for navigation
            item.addEventListener('click', (e) => {
                if (this.onAnnotationClick) {
                    // If Ctrl key is pressed, navigate to start of annotation (left-aligned)
                    // Otherwise, center the annotation in the view
                    if (e.ctrlKey) {
                        this.onAnnotationClick(annotation.start, true); // true indicates left-aligned
                    } else {
                        this.onAnnotationClick(annotation.start, false); // false indicates centered
                    }
                }
            });

            list.appendChild(item);
        });

        // Add search functionality
        this.addAnnotationSearch(listId);

        // After populating, update the visibility of items based on current filter settings
        this.updateAnnotationListVisibility(listId);
    }

    // Update the visibility of annotations in the list
    updateAnnotationListVisibility(listId) {
        const showRespiratory = document.getElementById('show-respiratory').checked;
        const showLegMovements = document.getElementById('show-leg-movements').checked;
        const showArousals = document.getElementById('show-arousals').checked;

        const items = document.querySelectorAll('.annotation-item');

        items.forEach(item => {
            const category = item.dataset.category;

            if (category === 'respiratory' && !showRespiratory) {
                item.style.opacity = '0.5';
            } else if (category === 'limb' && !showLegMovements) {
                item.style.opacity = '0.5';
            } else if (category === 'arousal' && !showArousals) {
                item.style.opacity = '0.5';
            } else {
                item.style.opacity = '1';
            }
        });
    }

    // Highlight annotations that are visible in the current window
    highlightVisibleAnnotations(listId) {
        const timeEnd = this.currentStartTime + this.windowSizeInSeconds;
        const visibleAnnotations = this.annotations.filter(a => {
            return (a.start >= this.currentStartTime && a.start < timeEnd) ||
                   (a.end > this.currentStartTime && a.end <= timeEnd) ||
                   (a.start <= this.currentStartTime && a.end >= timeEnd);
        });

        // Update annotation list highlighting
        const items = document.querySelectorAll('.annotation-item');
        items.forEach(item => {
            item.style.backgroundColor = '';
            item.style.fontWeight = 'normal';
        });

        let firstVisibleItem = null;
        visibleAnnotations.forEach(annotation => {
            const index = this.annotations.indexOf(annotation);
            if (index >= 0 && index < items.length) {
                items[index].style.backgroundColor = '#e6f7ff';
                items[index].style.fontWeight = 'bold';
                if (!firstVisibleItem) {
                    firstVisibleItem = items[index];
                }
            }
        });

        // Scroll to the first visible annotation if any are visible
        if (firstVisibleItem) {
            firstVisibleItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    // Get event type information
    getEventTypeInfo(annotation) {
        // Initialize default return values
        let result = {
            category: 'unknown',
            displayName: annotation.type_name || annotation.type,
            color: 'rgba(128, 128, 128, 0.2)', // Default gray
            borderColor: 'rgba(128, 128, 128, 0.8)',
            visible: true
        };

        // Check for sleep stage events by code
        if (annotation.type === '2:23672' ||
            annotation.type === '130834' ||
            annotation.type === '130835' ||
            annotation.type === '130836' ||
            annotation.type === '2:23680') {
            result.category = 'sleepStage';
            // Make sleep stages more transparent (0.1 instead of 0.2)
            result.color = 'rgba(0, 0, 255, 0.1)'; // Light blue with higher transparency
            result.borderColor = 'rgba(0, 0, 255, 0.4)'; // Also make borders less dominant

            // Map sleep stage codes to readable names
            if (annotation.type === '2:23672') {
                result.displayName = 'Wake';
            } else if (annotation.type === '130834') {
                result.displayName = 'N1';
            } else if (annotation.type === '130835') {
                result.displayName = 'N2';
            } else if (annotation.type === '130836') {
                result.displayName = 'N3';
            } else if (annotation.type === '2:23680') {
                result.displayName = 'REM';
                result.color = 'rgba(128, 0, 128, 0.1)'; // Very light purple instead of red
                result.borderColor = 'rgba(128, 0, 128, 0.4)';
            }

            result.visible = true; // Sleep stages are always visible
        }
        // Check for respiratory/apnea events
        else if (annotation.type === '3:3072') {
            result.category = 'respiratory';
            result.color = 'rgba(255, 0, 0, 0.2)'; // Red
            result.borderColor = 'rgba(255, 0, 0, 0.8)';
            result.visible = document.getElementById('show-respiratory').checked;
        }
        // Check for arousal events
        else if (annotation.type === '2:23800') {
            result.category = 'arousal';
            result.color = 'rgba(255, 165, 0, 0.2)'; // Orange
            result.borderColor = 'rgba(255, 165, 0, 0.8)';
            result.visible = document.getElementById('show-arousals').checked;
        }
        // Check for leg/limb movement events
        else if (annotation.type === '2:24184') {
            result.category = 'limb';
            result.color = 'rgba(0, 128, 0, 0.2)'; // Green
            result.borderColor = 'rgba(0, 128, 0, 0.8)';
            result.visible = document.getElementById('show-leg-movements').checked;
        } else {
            console.log("Unknown annotation type:", annotation);
        }

        return result;
    }

    // Set current time window
    setTimeWindow(startTime, windowSize) {
        this.currentStartTime = startTime;
        this.windowSizeInSeconds = windowSize;
    }

    // Populate events summary with counts
    populateEventsSummary(annotations, summaryId) {
        const summaryContainer = document.getElementById(summaryId);
        summaryContainer.innerHTML = '';

        // Count events by type
        const eventCounts = {};
        annotations.forEach(annotation => {
            const eventInfo = this.getEventTypeInfo(annotation);
            const key = eventInfo.displayName;
            
            if (!eventCounts[key]) {
                eventCounts[key] = {
                    count: 0,
                    category: eventInfo.category,
                    displayName: eventInfo.displayName
                };
            }
            eventCounts[key].count++;
        });

        // Sort by category and then by count (descending)
        const sortedEvents = Object.values(eventCounts).sort((a, b) => {
            // First sort by category priority
            const categoryOrder = { 'sleepStage': 1, 'respiratory': 2, 'arousal': 3, 'limb': 4, 'unknown': 5 };
            const categoryDiff = (categoryOrder[a.category] || 5) - (categoryOrder[b.category] || 5);
            if (categoryDiff !== 0) return categoryDiff;
            
            // Then sort by count (descending)
            return b.count - a.count;
        });

        // Create summary items
        sortedEvents.forEach(eventData => {
            const item = document.createElement('div');
            item.className = 'event-count-item';
            item.dataset.category = eventData.category;

            const nameSpan = document.createElement('span');
            nameSpan.className = 'event-type-name';
            nameSpan.textContent = eventData.displayName;

            const countSpan = document.createElement('span');
            countSpan.className = 'event-count';
            countSpan.textContent = eventData.count;

            item.appendChild(nameSpan);
            item.appendChild(countSpan);
            summaryContainer.appendChild(item);
        });
    }

    // Add search functionality to annotation list
    addAnnotationSearch(listId) {
        const container = document.getElementById(listId).parentElement;

        // Check if search input already exists
        if (container.querySelector('.annotation-search')) {
            return;
        }

        // Create search input
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'annotation-search';
        searchInput.placeholder = 'Search annotations...';
        searchInput.style.width = '100%';
        searchInput.style.marginBottom = '10px';
        searchInput.style.padding = '5px';
        searchInput.style.border = '1px solid #ddd';
        searchInput.style.borderRadius = '3px';

        // Insert search input before the annotation list
        const annotationList = document.getElementById(listId);
        container.insertBefore(searchInput, annotationList);

        // Add search functionality
        searchInput.addEventListener('input', (e) => {
            this.filterAnnotations(listId, e.target.value.toLowerCase());
        });
    }

    // Filter annotations based on search term
    filterAnnotations(listId, searchTerm) {
        const items = document.querySelectorAll(`#${listId} .annotation-item`);

        items.forEach(item => {
            const displayName = item.dataset.displayName.toLowerCase();
            const textContent = item.textContent.toLowerCase();

            if (searchTerm === '' || displayName.includes(searchTerm) || textContent.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // Highlight annotation in the list when clicked on canvas
    highlightAnnotationInList(annotationStartTime) {
        const items = document.querySelectorAll('.annotation-item');

        // Remove previous highlights
        items.forEach(item => {
            item.style.backgroundColor = '';
            item.style.fontWeight = '';
        });

        // Find and highlight the matching annotation
        items.forEach(item => {
            const startTime = parseFloat(item.dataset.startTime);
            if (Math.abs(startTime - annotationStartTime) < 0.1) { // Small tolerance for floating point comparison
                item.style.backgroundColor = '#e3f2fd';
                item.style.fontWeight = 'bold';

                // Scroll to the item
                item.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }
}

export default AnnotationManager;
