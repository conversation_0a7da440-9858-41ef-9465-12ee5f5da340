// DICOM Loader - Handles loading and processing DICOM files
class DicomLoader {
    constructor() {
        this.currentStudyId = null;
        this.selectedFiles = [];
    }

    // Load study data from server
    async loadStudyData(studyId) {
        try {
            const response = await fetch(`/api/study/${studyId}`);
            if (!response.ok) {
                throw new Error('Failed to load study data');
            }
            return await response.json();
        } catch (error) {
            console.error('Error loading study:', error);
            throw error;
        }
    }

    // Load study files and patient information
    async loadStudyFiles(studyId) {
        try {
            const response = await fetch(`/api/study/${studyId}/files`);
            if (!response.ok) {
                throw new Error('Failed to load study files');
            }
            return await response.json();
        } catch (error) {
            console.error('Error loading study files:', error);
            throw error;
        }
    }

    // Load signal data for a specific time window
    async loadSignalWindow(signals, timeStart, timeEnd) {
        const windowPromises = [];

        for (const signalFile of signals) {
            const promise = fetch(`/api/signal/${signalFile.type}?path=${encodeURIComponent(signalFile.path)}&start_time=${timeStart}&window_size=${timeEnd-timeStart}`)
                .then(response => response.json())
                .then(channelData => {const signalCanvas = document.getElementById(`signal-${signalFile.type}`);
                  
                if (signalCanvas && channelData.length > 0) {
                      const signalContainer = signalCanvas.closest('.signal-canvas');
                      if (signalContainer) {
                          const channelCount = channelData.length;
                          const signalHeight = (channelCount * 100) + 60; // 100px per channel + 60px for header and x-axis
                          signalContainer.style.height = `${signalHeight}px`;
                      }
                  }
                    
                    return {
                        type: signalFile.type,
                        data: channelData
                    };
                });

            windowPromises.push(promise);
        }

        // Wait for all signal data to load
        const results = await Promise.all(windowPromises);
        
        // Convert array of results to object keyed by signal type
        const signalData = {};
        results.forEach(result => {
            signalData[result.type] = result.data;
        });
        
        return signalData;
    }

    // Upload files to server
    async uploadFiles(files) {
        const formData = new FormData();
        files.forEach(file => {
            formData.append('files[]', file);
        });

        const response = await fetch('/api/upload-files', {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }

    // Add files to an existing study
    async addFilesToStudy(studyId, files) {
        const formData = new FormData();
        Array.from(files).forEach(file => {
            formData.append('files[]', file);
        });

        const response = await fetch(`/api/study/${studyId}/files`, {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }

    // Delete a file from study
    async deleteStudyFile(studyId, filename) {
        const response = await fetch(`/api/study/${studyId}/files/${filename}`, {
            method: 'DELETE'
        });

        return await response.json();
    }

    // Clean up study on server
    async cleanupStudy(studyId) {
        await fetch(`/api/cleanup/${studyId}`, {
            method: 'DELETE'
        });
    }
}

export default DicomLoader;