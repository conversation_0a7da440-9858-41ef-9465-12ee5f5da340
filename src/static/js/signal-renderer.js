// Signal Renderer - <PERSON><PERSON> rendering signal data to canvas
class SignalRenderer {
    constructor() {
        this.signalData = {};
        this.currentStartTime = 0;
        this.windowSizeInSeconds = 30;
        this.dynamicScaling = true; // Default to dynamic scaling
        this.channelVisibility = {}; // Track which channels are visible per signal type
        this.channelOrders = {}; // Track reordered channels per signal type

        // AASM default scaling values (in microvolts) for different signal types
        this.aasmDefaults = {
            'eeg': 50,           // EEG: 50 μV
            'eog': 100,          // EOG: 100 μV
            'emg': 20,           // EMG: 20 μV
            'ecg': 500,          // ECG: 500 μV
            'respiratory': 1000, // Respiratory: 1000 μV (1 mV)
            'audio': 1000,       // Audio: 1000 μV
            'position': 100,     // Body Position: 100 μV
            'pulse': 500,        // Arterial Pulse: 500 μV
            'default': 50        // Default fallback: 50 μV
        };
    }

    // Initialize signal containers
    initializeSignalContainers(signalFiles, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        for (const signalFile of signalFiles) {
            // Create canvas for each signal type
            const canvasDiv = document.createElement('div');
            canvasDiv.className = 'signal-canvas';
            canvasDiv.style.height = '160px'; // Default height
            canvasDiv.draggable = true;
            canvasDiv.dataset.signalType = signalFile.type;

            // Format the signal type name for better display
            const signalTypeName = signalFile.type.replace(/([A-Z])/g, ' $1').trim();
            const formattedType = signalTypeName.charAt(0).toUpperCase() + signalTypeName.slice(1);

            canvasDiv.innerHTML = `
                <div class="signal-header">
                    ${formattedType}
                    <div class="signal-drag-handle" title="Drag to reorder"></div>
                </div>
                <div class="signal-plot-area">
                    <div class="channel-labels" id="labels-${signalFile.type}"></div>
                    <canvas id="signal-${signalFile.type}"></canvas>
                    <div class="signal-x-axis" id="axis-${signalFile.type}"></div>
                </div>
            `;
            container.appendChild(canvasDiv);
        }

        // Add drag and drop functionality
        this.addDragAndDropFunctionality(containerId);

        // Add channel drag and drop functionality
        this.addChannelDragAndDropFunctionality(containerId);
    }

    // Get scaling value based on current mode and signal type
    getScalingValue(signalType, visibleData) {
        if (this.dynamicScaling) {
            // Dynamic scaling: scale around min/max values
            const minValue = Math.min(...visibleData);
            const maxValue = Math.max(...visibleData);
            const range = Math.max(Math.abs(minValue), Math.abs(maxValue));
            return range || 1;
        } else {
            // AASM default scaling: use predefined values
            const signalTypeLower = signalType.toLowerCase();

            // Map signal types to AASM categories
            if (signalTypeLower.includes('eeg') || signalTypeLower.includes('electroencephalogram')) {
                return this.aasmDefaults.eeg;
            } else if (signalTypeLower.includes('eog') || signalTypeLower.includes('electrooculogram')) {
                return this.aasmDefaults.eog;
            } else if (signalTypeLower.includes('emg') || signalTypeLower.includes('electromyogram')) {
                return this.aasmDefaults.emg;
            } else if (signalTypeLower.includes('ecg') || signalTypeLower.includes('electrocardiogram')) {
                return this.aasmDefaults.ecg;
            } else if (signalTypeLower.includes('respiratory') || signalTypeLower.includes('respiration')) {
                return this.aasmDefaults.respiratory;
            } else if (signalTypeLower.includes('audio')) {
                return this.aasmDefaults.audio;
            } else if (signalTypeLower.includes('position')) {
                return this.aasmDefaults.position;
            } else if (signalTypeLower.includes('pulse')) {
                return this.aasmDefaults.pulse;
            } else {
                return this.aasmDefaults.default;
            }
        }
    }

    // Draw signal data on canvas
    drawSignal(signalType, timeStart, timeEnd, annotations) {
        if (!this.signalData[signalType]) return;

        const canvas = document.getElementById(`signal-${signalType}`);
        const ctx = canvas.getContext('2d');
        const labelsContainer = document.getElementById(`labels-${signalType}`);

        // Set canvas size
        canvas.width = canvas.parentElement.clientWidth - 110;
        canvas.height = canvas.parentElement.clientHeight - 30;
        canvas.style.marginLeft = '110px';

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        labelsContainer.innerHTML = '';

        const channels = this.signalData[signalType];
        if (channels.length === 0) return;

        // For each channel in this signal type
        channels.forEach((channel, idx) => {
            const visibleData = channel.data;
            const scalingValue = this.getScalingValue(signalType, visibleData);

            // Use consistent height for each channel
            const fixedChannelHeight = 100;
            const buffer = 10;
            const adjustedChannelHeight = fixedChannelHeight - buffer;
            const yOffset = idx * fixedChannelHeight + adjustedChannelHeight/2 + buffer;

            // Draw the signal
            ctx.beginPath();
            ctx.moveTo(0, yOffset);

            for (let i = 0; i < visibleData.length; i++) {
                const x = (i / visibleData.length) * canvas.width;

                let amplitude;
                if (this.dynamicScaling) {
                    // Dynamic scaling: center around the middle of min/max range
                    const minValue = Math.min(...visibleData);
                    const maxValue = Math.max(...visibleData);
                    const midValue = (minValue + maxValue) / 2;
                    const range = Math.max(Math.abs(maxValue - midValue), Math.abs(minValue - midValue)) || 1;
                    amplitude = ((visibleData[i] - midValue) / range) * (adjustedChannelHeight/2 - 15);
                } else {
                    // AASM default scaling: center around 0
                    amplitude = (visibleData[i] / scalingValue) * (adjustedChannelHeight/2 - 15);
                }

                const y = yOffset - amplitude;
                ctx.lineTo(x, y);
            }

            // Use different colors for different channels
            const colors = ['#2196F3', '#4CAF50', '#9C27B0', '#FF5722', '#795548'];
            ctx.strokeStyle = colors[idx % colors.length];
            ctx.lineWidth = 1.5;
            ctx.stroke();

            // Add channel label with management controls
            const labelDiv = document.createElement('div');
            labelDiv.className = 'channel-label';
            labelDiv.style.position = 'absolute';
            labelDiv.style.top = `${idx * fixedChannelHeight + (fixedChannelHeight/2) - 20}px`;
            labelDiv.style.borderLeftColor = colors[idx % colors.length];
            labelDiv.draggable = true;
            labelDiv.dataset.channelIndex = idx;
            labelDiv.dataset.signalType = signalType;

            // Create label content container
            const labelContent = document.createElement('div');
            labelContent.className = 'channel-label-content';

            // Create text container
            const labelText = document.createElement('div');
            labelText.className = 'channel-label-text';
            labelText.textContent = channel.name;

            // Create controls container
            const controlsDiv = document.createElement('div');
            controlsDiv.className = 'channel-controls';

            // Add drag handle
            const dragHandle = document.createElement('button');
            dragHandle.className = 'channel-drag-handle';
            dragHandle.innerHTML = '⋮⋮';
            dragHandle.title = 'Drag to reorder';
            dragHandle.addEventListener('mousedown', (e) => e.stopPropagation());

            // Add remove button
            const removeBtn = document.createElement('button');
            removeBtn.className = 'channel-remove-btn';
            removeBtn.innerHTML = '×';
            removeBtn.title = 'Remove channel';
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.removeChannel(signalType, idx);
            });

            controlsDiv.appendChild(dragHandle);
            controlsDiv.appendChild(removeBtn);

            labelContent.appendChild(labelText);
            labelContent.appendChild(controlsDiv);
            labelDiv.appendChild(labelContent);

            // Add units if available
            const unitsSpan = document.createElement('span');
            unitsSpan.className = 'channel-units';
            unitsSpan.textContent = channel.units || 'μV';
            labelDiv.appendChild(unitsSpan);

            // Add value range based on scaling mode
            const rangeSpan = document.createElement('span');
            rangeSpan.className = 'channel-range';

            if (this.dynamicScaling) {
                const minValue = Math.min(...visibleData);
                const maxValue = Math.max(...visibleData);
                rangeSpan.textContent = `Range: ${minValue.toFixed(1)} to ${maxValue.toFixed(1)} ${channel.units || 'μV'}`;
            } else {
                rangeSpan.textContent = `Scale: ±${scalingValue.toFixed(1)} ${channel.units || 'μV'} (AASM)`;
            }

            labelDiv.appendChild(rangeSpan);

            labelsContainer.appendChild(labelDiv);
        });

        // Draw x-axis timestamps
        this.drawTimeAxis(signalType, timeStart, timeEnd);

        // Draw visible annotations on the signal
        this.drawAnnotationsOnSignal(signalType, timeStart, timeEnd, annotations);

        // Add hover functionality to show signal values
        this.addSignalValueHover(signalType, timeStart, timeEnd);
    }

    // Draw time axis for a signal
    drawTimeAxis(signalType, timeStart, timeEnd) {
        const axisDiv = document.getElementById(`axis-${signalType}`);
        axisDiv.innerHTML = '';

        const duration = timeEnd - timeStart;
        // Calculate appropriate number of ticks based on duration
        let numTicks;
        if (duration <= 10) {
            numTicks = duration;
        } else if (duration <= 30) {
            numTicks = Math.floor(duration / 2);
        } else if (duration <= 60) {
            numTicks = Math.floor(duration / 5);
        } else {
            numTicks = Math.floor(duration / 10);
        }

        numTicks = Math.max(5, Math.min(numTicks, 15));

        // Add a title to the axis
        const axisTitle = document.createElement('div');
        axisTitle.style.position = 'absolute';
        axisTitle.style.left = '50%';
        axisTitle.style.transform = 'translateX(-50%)';
        axisTitle.style.bottom = '0';
        axisTitle.style.fontSize = '10px';
        axisTitle.style.fontWeight = 'bold';
        axisTitle.style.color = '#666';
        axisTitle.textContent = 'Time (mm:ss)';
        axisDiv.appendChild(axisTitle);

        // Draw the main axis line
        const axisLine = document.createElement('div');
        axisLine.style.position = 'absolute';
        axisLine.style.left = '0';
        axisLine.style.right = '0';
        axisLine.style.top = '0';
        axisLine.style.height = '2px';
        axisLine.style.backgroundColor = '#666';
        axisDiv.appendChild(axisLine);

        for (let i = 0; i <= numTicks; i++) {
            const position = (i / numTicks);
            const time = timeStart + position * duration;
            const minutes = Math.floor(time / 60);
            const seconds = Math.floor(time % 60);

            // Create tick mark
            const tickMark = document.createElement('div');
            tickMark.className = 'time-tick-mark';
            tickMark.style.left = `${position * 100}%`;
            tickMark.style.top = '0';
            tickMark.style.height = '8px';
            axisDiv.appendChild(tickMark);

            // Create tick label
            const tickLabel = document.createElement('div');
            tickLabel.className = 'time-tick';
            tickLabel.style.left = `${position * 100}%`;
            tickLabel.style.top = '10px';
            tickLabel.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            axisDiv.appendChild(tickLabel);
        }
    }

    // Draw annotations on a signal
    drawAnnotationsOnSignal(signalType, timeStart, timeEnd, annotations) {
        const canvas = document.getElementById(`signal-${signalType}`);
        const ctx = canvas.getContext('2d');

        // Remove any existing tooltips
        document.querySelectorAll('.annotation-tooltip').forEach(el => el.remove());

        // Find annotations in the current time window
        const visibleAnnotations = annotations.filter(a => {
            return (a.start >= timeStart && a.start < timeEnd) ||
                (a.end > timeStart && a.end <= timeEnd) ||
                (a.start <= timeStart && a.end >= timeEnd);
        });

        // Draw each annotation
        visibleAnnotations.forEach(annotation => {
            // Get event type information
            const eventInfo = this.getEventTypeInfo(annotation);

            // Skip if not visible
            if (!eventInfo.visible) return;

            // Calculate position
            const startX = Math.max(0, ((annotation.start - timeStart) / (timeEnd - timeStart)) * canvas.width);
            const endX = Math.min(canvas.width, ((annotation.end - timeStart) / (timeEnd - timeStart)) * canvas.width);

            // Draw annotation background with gradient
            const gradient = ctx.createLinearGradient(startX, 0, endX, 0);
            gradient.addColorStop(0, eventInfo.color);
            gradient.addColorStop(1, eventInfo.color.replace('0.2', '0.1'));
            ctx.fillStyle = gradient;
            ctx.fillRect(startX, 0, endX - startX, canvas.height);

            // Draw annotation border
            ctx.strokeStyle = eventInfo.borderColor;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(startX, 0);
            ctx.lineTo(startX, canvas.height);
            ctx.moveTo(endX, 0);
            ctx.lineTo(endX, canvas.height);
            ctx.stroke();

            // Draw annotation label if it fits
            if (endX - startX > 40) {
                ctx.font = 'bold 10px Arial';
                const textWidth = ctx.measureText(eventInfo.displayName).width;

                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                const bgX = startX + 2;
                const bgY = eventInfo.category === 'sleepStage' ? 5 : 25;
                const bgWidth = textWidth + 6;
                const bgHeight = 14;
                const radius = 3;

                // Draw rounded rectangle
                ctx.beginPath();
                ctx.moveTo(bgX + radius, bgY);
                ctx.lineTo(bgX + bgWidth - radius, bgY);
                ctx.quadraticCurveTo(bgX + bgWidth, bgY, bgX + bgWidth, bgY + radius);
                ctx.lineTo(bgX + bgWidth, bgY + bgHeight - radius);
                ctx.quadraticCurveTo(bgX + bgWidth, bgY + bgHeight, bgX + bgWidth - radius, bgY + bgHeight);
                ctx.lineTo(bgX + radius, bgY + bgHeight);
                ctx.quadraticCurveTo(bgX, bgY + bgHeight, bgX, bgY + bgHeight - radius);
                ctx.lineTo(bgX, bgY + radius);
                ctx.quadraticCurveTo(bgX, bgY, bgX + radius, bgY);
                ctx.closePath();
                ctx.fill();

                // Draw text with slight shadow for better readability
                ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
                ctx.fillText(eventInfo.displayName, startX + 4, bgY + 11);
                ctx.fillStyle = 'black';
                ctx.fillText(eventInfo.displayName, startX + 3, bgY + 10);
            }

            // Add hover functionality for annotations
            const annotationArea = document.createElement('div');
            annotationArea.style.position = 'absolute';
            annotationArea.style.left = `${startX + 110}px`; // Account for label container
            annotationArea.style.top = '0';
            annotationArea.style.width = `${endX - startX}px`;
            annotationArea.style.height = `${canvas.height}px`;
            annotationArea.style.zIndex = '5';
            annotationArea.style.cursor = 'pointer';

            // Add tooltip on hover
            annotationArea.addEventListener('mouseover', (e) => {
                const tooltip = document.createElement('div');
                tooltip.className = 'annotation-tooltip';
                tooltip.textContent = `${eventInfo.displayName} (${Math.floor(annotation.start / 60)}:${Math.floor(annotation.start % 60).toString().padStart(2, '0')} - ${Math.floor(annotation.end / 60)}:${Math.floor(annotation.end % 60).toString().padStart(2, '0')})`;
                tooltip.style.left = `${e.clientX + 10}px`;
                tooltip.style.top = `${e.clientY + 10}px`;
                document.body.appendChild(tooltip);
            });

            annotationArea.addEventListener('mousemove', (e) => {
                const tooltip = document.querySelector('.annotation-tooltip');
                if (tooltip) {
                    tooltip.style.left = `${e.clientX + 10}px`;
                    tooltip.style.top = `${e.clientY + 10}px`;
                }
            });

            annotationArea.addEventListener('mouseout', () => {
                const tooltip = document.querySelector('.annotation-tooltip');
                if (tooltip) tooltip.remove();
            });

            // Navigate to annotation on click
            annotationArea.addEventListener('click', (e) => {
                if (this.onAnnotationClick) {
                    // If Ctrl key is pressed, navigate to start of annotation (left-aligned)
                    // Otherwise, center the annotation in the view
                    if (e.ctrlKey) {
                        this.onAnnotationClick(annotation.start, true); // true indicates left-aligned
                    } else {
                        this.onAnnotationClick(annotation.start, false); // false indicates centered
                    }
                }

                // Highlight annotation in the list
                if (this.onAnnotationListHighlight) {
                    this.onAnnotationListHighlight(annotation.start);
                }
            });

            canvas.parentElement.appendChild(annotationArea);
        });
    }

    // Add hover functionality to show signal values
    addSignalValueHover(signalType, timeStart, timeEnd) {
        const canvas = document.getElementById(`signal-${signalType}`);
        const plotArea = canvas.parentElement;
        const channels = this.signalData[signalType];

        if (!channels || channels.length === 0) return;

        // Remove any existing hover overlay
        const existingOverlay = plotArea.querySelector('.signal-hover-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create a transparent overlay for mouse tracking
        const overlay = document.createElement('div');
        overlay.className = 'signal-hover-overlay';
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '110px'; // Match the canvas offset
        overlay.style.width = `${canvas.width}px`;
        overlay.style.height = `${canvas.height}px`;
        overlay.style.zIndex = '20';
        overlay.style.cursor = 'crosshair';

        // Add mouse event listeners
        overlay.addEventListener('mousemove', (e) => {
            // Calculate relative position within the canvas
            const rect = overlay.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Calculate time at this x position
            const timeRatio = x / canvas.width;
            const currentTime = timeStart + (timeEnd - timeStart) * timeRatio;

            // Format time as mm:ss
            const minutes = Math.floor(currentTime / 60);
            const seconds = Math.floor(currentTime % 60);
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            // Find the closest data point for each channel
            let tooltipHTML = '';

            channels.forEach((channel, idx) => {
                const dataIndex = Math.floor((channel.data.length - 1) * timeRatio);
                if (dataIndex >= 0 && dataIndex < channel.data.length) {
                    const value = channel.data[dataIndex];
                    const units = channel.units || 'μV';

                    // Add to tooltip content - more compact format
                    tooltipHTML += `<div style="color:${['#2196F3', '#4CAF50', '#9C27B0', '#FF5722', '#795548'][idx % 5]}; margin:2px 0; white-space:nowrap;">
                        <b>${channel.name}:</b> ${value.toFixed(2)} ${units}
                    </div>`;
                }
            });

            // Add time to tooltip
            tooltipHTML += `<div style="margin-top:4px; border-top:1px solid #555; padding-top:3px; text-align:center; font-weight:bold;">
                ${timeString}
            </div>`;

            // Create a compact tooltip that stays near the signal
            let tooltip = document.querySelector('.signal-tooltip');
            if (!tooltip) {
                tooltip = document.createElement('div');
                tooltip.className = 'signal-tooltip';
                document.body.appendChild(tooltip);
            }

            tooltip.innerHTML = tooltipHTML;

            // Position the tooltip near the cursor but fixed to the signal area
            const plotRect = plotArea.getBoundingClientRect();

            // Calculate position to keep tooltip within the viewport
            let tooltipX = e.clientX + 15;
            let tooltipY = e.clientY - 40; // Position above cursor

            // Keep tooltip within the signal area horizontally
            if (tooltipX + 200 > plotRect.right) { // Assuming max tooltip width is 200px
                tooltipX = e.clientX - 215; // Position to the left of cursor
            }

            // Keep tooltip within the viewport vertically
            if (tooltipY < 10) {
                tooltipY = e.clientY + 25; // Position below cursor if too close to top
            }

            tooltip.style.left = `${tooltipX}px`;
            tooltip.style.top = `${tooltipY}px`;

            // Draw vertical line at cursor position
            this.drawCursorLine(signalType, x);
        });

        overlay.addEventListener('mouseout', () => {
            // Remove tooltip and cursor line when mouse leaves
            const tooltip = document.querySelector('.signal-tooltip');
            if (tooltip) tooltip.remove();

            // Remove cursor line
            this.removeCursorLine(signalType);
        });

        plotArea.appendChild(overlay);
    }

    // Draw a vertical line at cursor position
    drawCursorLine(signalType, x) {
        const canvas = document.getElementById(`signal-${signalType}`);
        const plotArea = canvas.parentElement;

        // Remove any existing cursor line
        this.removeCursorLine(signalType);

        // Create cursor line
        const cursorLine = document.createElement('div');
        cursorLine.className = 'signal-cursor-line';
        cursorLine.style.position = 'absolute';
        cursorLine.style.top = '0';
        cursorLine.style.left = `${x + 110}px`; // Add offset to match canvas
        cursorLine.style.width = '2px'; // Slightly thicker for better visibility
        cursorLine.style.height = '100%';
        cursorLine.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
        cursorLine.style.zIndex = '15';
        cursorLine.style.pointerEvents = 'none'; // Ensure it doesn't interfere with mouse events

        // Add a small circle at the intersection with the signal
        const dataPoint = document.createElement('div');
        dataPoint.className = 'signal-data-point';
        dataPoint.style.position = 'absolute';
        dataPoint.style.width = '8px';
        dataPoint.style.height = '8px';
        dataPoint.style.borderRadius = '50%';
        dataPoint.style.backgroundColor = '#2196F3';
        dataPoint.style.border = '1px solid white';
        dataPoint.style.left = `${x + 110 - 4}px`; // Center on the line
        dataPoint.style.top = '50%'; // Will be adjusted by channel data
        dataPoint.style.zIndex = '16';
        dataPoint.style.pointerEvents = 'none';

        plotArea.appendChild(cursorLine);

        // Only add data point if we have channel data
        const channels = this.signalData[signalType];
        if (channels && channels.length > 0) {
            // For simplicity, just show for the first channel
            plotArea.appendChild(dataPoint);
        }
    }

    // Remove cursor line and data point
    removeCursorLine(signalType) {
        const canvas = document.getElementById(`signal-${signalType}`);
        const plotArea = canvas.parentElement;

        const cursorLine = plotArea.querySelector('.signal-cursor-line');
        if (cursorLine) cursorLine.remove();

        const dataPoint = plotArea.querySelector('.signal-data-point');
        if (dataPoint) dataPoint.remove();
    }

    // Get event type information
    getEventTypeInfo(annotation) {
        // Initialize default return values
        let result = {
            category: 'unknown',
            displayName: annotation.type_name || annotation.type,
            color: 'rgba(128, 128, 128, 0.2)', // Default gray
            borderColor: 'rgba(128, 128, 128, 0.8)',
            visible: true
        };

        // Check for sleep stage events by code
        if (annotation.type === '2:23672' ||
            annotation.type === '130834' ||
            annotation.type === '130835' ||
            annotation.type === '130836' ||
            annotation.type === '2:23680') {
            result.category = 'sleepStage';
            // Make sleep stages more transparent (0.1 instead of 0.2)
            result.color = 'rgba(0, 0, 255, 0.1)'; // Light blue with higher transparency
            result.borderColor = 'rgba(0, 0, 255, 0.4)'; // Also make borders less dominant

            // Map sleep stage codes to readable names
            if (annotation.type === '2:23672') {
                result.displayName = 'Wake';
            } else if (annotation.type === '130834') {
                result.displayName = 'N1';
            } else if (annotation.type === '130835') {
                result.displayName = 'N2';
            } else if (annotation.type === '130836') {
                result.displayName = 'N3';
            } else if (annotation.type === '2:23680') {
                result.displayName = 'REM';
                result.color = 'rgba(128, 0, 128, 0.1)'; // Very light purple instead of red
                result.borderColor = 'rgba(128, 0, 128, 0.4)';
            }

            result.visible = true; // Sleep stages are always visible
        }
        // Check for respiratory/apnea events
        else if (annotation.type === '3:3072') {
            result.category = 'respiratory';
            result.color = 'rgba(255, 0, 0, 0.2)'; // Red
            result.borderColor = 'rgba(255, 0, 0, 0.8)';
            result.visible = document.getElementById('show-respiratory').checked;
        }
        // Check for arousal events
        else if (annotation.type === '2:23800') {
            result.category = 'arousal';
            result.color = 'rgba(255, 165, 0, 0.2)'; // Orange
            result.borderColor = 'rgba(255, 165, 0, 0.8)';
            result.visible = document.getElementById('show-arousals').checked;
        }
        // Check for leg/limb movement events
        else if (annotation.type === '2:24184') {
            result.category = 'limb';
            result.color = 'rgba(0, 128, 0, 0.2)'; // Green
            result.borderColor = 'rgba(0, 128, 0, 0.8)';
            result.visible = document.getElementById('show-leg-movements').checked;
        }

        return result;
    }

    // Set the signal data
    setSignalData(signalData) {
        // Store the original data and apply visibility/orders
        this.originalSignalData = signalData;
        this.signalData = {};

        for (const [signalType, channels] of Object.entries(signalData)) {
            // Initialize visibility if not set (all channels visible by default)
            if (!this.channelVisibility[signalType]) {
                this.channelVisibility[signalType] = channels.map((_, index) => index);
            }

            // Get visible channels
            let visibleChannels = this.channelVisibility[signalType]
                .filter(index => index < channels.length)
                .map(index => channels[index]);

            // Apply channel reordering if any
            if (this.channelOrders[signalType]) {
                const reorderedChannels = [];
                this.channelOrders[signalType].forEach(originalIndex => {
                    const visibleIndex = this.channelVisibility[signalType].indexOf(originalIndex);
                    if (visibleIndex !== -1 && originalIndex < channels.length) {
                        reorderedChannels.push(channels[originalIndex]);
                    }
                });
                visibleChannels = reorderedChannels;
            }

            this.signalData[signalType] = visibleChannels;
        }
    }

    // Toggle dynamic scaling mode
    toggleDynamicScaling() {
        this.dynamicScaling = !this.dynamicScaling;

        // Update the toggle button text/state if it exists
        const toggleButton = document.getElementById('dynamic-scaling-toggle');
        if (toggleButton) {
            toggleButton.textContent = this.dynamicScaling ? 'Switch to AASM Scaling' : 'Switch to Dynamic Scaling';
            toggleButton.title = this.dynamicScaling ?
                'Currently using dynamic scaling (min/max). Click to use AASM default scaling.' :
                'Currently using AASM default scaling. Click to use dynamic scaling (min/max).';
        }

        // Note: We don't trigger redraw here since the main app handles it
        // This prevents infinite loops and gives the main app control over when to redraw
    }

    // Get current scaling mode
    isDynamicScaling() {
        return this.dynamicScaling;
    }

    // Add drag and drop functionality for reordering signal groups
    addDragAndDropFunctionality(containerId) {
        const container = document.getElementById(containerId);
        let draggedElement = null;

        container.addEventListener('dragstart', (e) => {
            if (e.target.classList.contains('signal-canvas')) {
                draggedElement = e.target;
                e.target.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', e.target.outerHTML);
            }
        });

        container.addEventListener('dragend', (e) => {
            if (e.target.classList.contains('signal-canvas')) {
                e.target.classList.remove('dragging');
                draggedElement = null;
            }
        });

        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            const afterElement = this.getDragAfterElement(container, e.clientY);
            const dragging = document.querySelector('.dragging');

            if (afterElement == null) {
                container.appendChild(dragging);
            } else {
                container.insertBefore(dragging, afterElement);
            }
        });

        container.addEventListener('drop', (e) => {
            e.preventDefault();

            // Remove drag-over styling
            document.querySelectorAll('.signal-canvas').forEach(canvas => {
                canvas.classList.remove('drag-over');
            });

            // Trigger redraw of all signals to maintain proper order
            if (this.onSignalReorder) {
                this.onSignalReorder();
            }
        });

        // Add dragover styling
        container.addEventListener('dragenter', (e) => {
            if (e.target.classList.contains('signal-canvas') && e.target !== draggedElement) {
                e.target.classList.add('drag-over');
            }
        });

        container.addEventListener('dragleave', (e) => {
            if (e.target.classList.contains('signal-canvas')) {
                e.target.classList.remove('drag-over');
            }
        });
    }

    // Helper function to determine where to insert the dragged element
    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.signal-canvas:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    // Remove a channel from a signal group
    removeChannel(signalType, channelIndex) {
        if (!this.signalData[signalType] || channelIndex < 0 || channelIndex >= this.signalData[signalType].length) {
            return;
        }

        // Initialize channel visibility for this signal type if not exists
        if (!this.channelVisibility[signalType]) {
            this.channelVisibility[signalType] = this.originalSignalData[signalType].map((_, index) => index);
        }

        // Find the original channel index that corresponds to the current displayed channel
        const originalChannelIndex = this.channelVisibility[signalType][channelIndex];

        // Remove the channel from visibility list
        this.channelVisibility[signalType].splice(channelIndex, 1);

        // Check if all channels are removed
        if (this.channelVisibility[signalType].length === 0) {
            // Remove the entire signal group
            delete this.signalData[signalType];
            delete this.channelVisibility[signalType];
            delete this.channelOrders[signalType];

            // Remove the signal canvas element
            const signalCanvas = document.getElementById(`signal-${signalType}`);
            if (signalCanvas) {
                const signalContainer = signalCanvas.closest('.signal-canvas');
                if (signalContainer) {
                    signalContainer.remove();
                }
            }

            // Notify parent about signal group removal
            if (this.onSignalGroupRemoved) {
                this.onSignalGroupRemoved(signalType);
            }
        } else {
            // Redraw the signal with remaining channels
            if (this.onChannelRemoved) {
                this.onChannelRemoved(signalType, originalChannelIndex);
            }
        }
    }

    // Add drag and drop functionality for individual channels
    addChannelDragAndDropFunctionality(containerId) {
        const container = document.getElementById(containerId);
        let draggedChannel = null;
        let draggedFromSignalType = null;
        let originalChannelIndex = null;

        container.addEventListener('dragstart', (e) => {
            if (e.target.classList.contains('channel-label')) {
                draggedChannel = e.target;
                draggedFromSignalType = e.target.dataset.signalType;
                originalChannelIndex = parseInt(e.target.dataset.channelIndex);
                e.target.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/plain', ''); // Required for Firefox
            }
        });

        container.addEventListener('dragend', (e) => {
            if (e.target.classList.contains('channel-label')) {
                e.target.classList.remove('dragging');
                draggedChannel = null;
                draggedFromSignalType = null;
                originalChannelIndex = null;
            }
        });

        container.addEventListener('dragover', (e) => {
            if (!draggedChannel) return;

            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            const targetLabelsContainer = e.target.closest('.channel-labels');
            if (!targetLabelsContainer) return;

            const afterElement = this.getChannelDragAfterElement(targetLabelsContainer, e.clientY);
            const dragging = document.querySelector('.channel-label.dragging');

            if (afterElement == null) {
                targetLabelsContainer.appendChild(dragging);
            } else {
                targetLabelsContainer.insertBefore(dragging, afterElement);
            }
        });

        container.addEventListener('drop', (e) => {
            if (!draggedChannel) return;

            e.preventDefault();

            const targetLabelsContainer = e.target.closest('.channel-labels');
            if (!targetLabelsContainer) return;

            // Get the new order of channels within this signal group
            const signalType = draggedFromSignalType;
            const channelLabels = [...targetLabelsContainer.querySelectorAll('.channel-label')];
            const newOrder = channelLabels.map(label => parseInt(label.dataset.channelIndex));

            // Only reorder if the order actually changed
            const originalOrder = this.signalData[signalType].map((_, idx) => idx);
            if (JSON.stringify(newOrder) !== JSON.stringify(originalOrder)) {
                this.reorderChannels(signalType, newOrder);
            }
        });
    }

    // Get channel element after which to insert dragged channel
    getChannelDragAfterElement(container, y) {
        if (!container) return null;

        const draggableElements = [...container.querySelectorAll('.channel-label:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    // Reorder channels within a signal group
    reorderChannels(signalType, newOrder) {
        if (!this.signalData[signalType]) return;

        // Store the new order for this signal type
        this.channelOrders[signalType] = newOrder;

        // Notify parent about channel reorder
        if (this.onChannelReordered) {
            this.onChannelReordered(signalType, newOrder);
        }
    }
}

export default SignalRenderer;
