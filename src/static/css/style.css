body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
}

.container {
  display: flex;
  height: 90vh;
}

.sidebar {
  width: 300px;
  border-right: 1px solid #ccc;
  padding: 10px;
  overflow: auto;
}

.main-view {
  flex: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.hypnogram {
  height: 150px;
  margin-bottom: 20px;
  border: 1px solid #ddd;
}

.signals-container {
  flex: 1;
  overflow: auto;
}

/* Improved signal canvas layout */
.signal-canvas {
  width: 100%;
  /* Height will be set dynamically based on number of channels */
  margin-bottom: 30px;
  /* Increased margin between signal groups */
  border: 1px solid #ddd;
  border-radius: 4px;
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  /* Add subtle shadow for better separation */
  overflow: visible;
  /* Allow content to be visible outside the container */
}

.signal-header {
  height: 30px;
  background-color: #f0f7ff;
  /* Light blue background */
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  padding: 0 10px;
  font-weight: bold;
  border-left: 4px solid #2196F3;
  /* Blue accent for headers */
  font-size: 14px;
  /* Larger font for better readability */
  letter-spacing: 0.5px;
  /* Slightly spaced letters */
}

.signal-plot-area {
  flex: 1;
  position: relative;
  overflow: hidden;
  /* Keep the plot area content contained */
  margin-bottom: 0;
  /* Ensure no margin at the bottom */
  padding-bottom: 0;
  /* Ensure no padding at the bottom */
}

.channel-labels {
  position: absolute;
  left: 0;
  top: 0;
  width: 100px;
  /* Wider to accommodate units and ranges */
  height: 100%;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.95);
  /* More opaque background */
  border-right: 1px solid #ddd;
  padding: 5px;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
  /* Subtle shadow for depth */
  overflow: visible;
  /* Allow content to overflow if needed */
}

.channel-label {
  font-size: 10px;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: rgba(245, 245, 245, 0.8);
  /* Light background for better readability */
  padding: 2px 4px;
  border-radius: 2px;
  border-left: 3px solid #2196F3;
  /* Blue accent for labels */
}

.channel-units {
  font-size: 8px;
  color: #666;
  display: block;
  margin-top: 2px;
}

.channel-range {
  font-size: 8px;
  color: #666;
  display: block;
  margin-top: 1px;
}

.signal-x-axis {
  height: 30px;
  width: 100%;
  /* Full width of the plot area */
  position: absolute;
  bottom: 0;
  /* Position at the bottom of the plot area */
  left: 0;
  background-color: #f9f9f9;
  border-top: 1px solid #ddd;
  /* Add top border for separation */
  padding-bottom: 5px;
  /* Add padding at the bottom for the label */
  z-index: 5;
  /* Ensure axis is above other elements */
}

.time-tick {
  position: absolute;
  font-size: 9px;
  color: #666;
  text-align: center;
  width: 40px;
  margin-left: -20px;
}

.time-tick-mark {
  position: absolute;
  height: 5px;
  width: 1px;
  background-color: #999;
}

.controls {
  margin-bottom: 20px;
}

.events-summary {
  padding: 10px 0;
}

.event-count-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

.event-count-item:last-child {
  border-bottom: none;
}

.event-type-name {
  font-weight: 500;
  color: #333;
}

.event-count {
  background-color: #f0f0f0;
  color: #666;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.event-count-item[data-category="sleepStage"] .event-count {
  background-color: #e3f2fd;
  color: #1976d2;
}

.event-count-item[data-category="respiratory"] .event-count {
  background-color: #ffebee;
  color: #d32f2f;
}

.event-count-item[data-category="arousal"] .event-count {
  background-color: #fff3e0;
  color: #f57c00;
}

.event-count-item[data-category="limb"] .event-count {
  background-color: #e8f5e8;
  color: #388e3c;
}

.annotation-list {
  overflow: auto;
  max-height: 300px;
}

.annotation-item {
  padding: 5px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
}

.annotation-item:hover {
  background-color: #f5f5f5;
}

.window-size {
  margin: 10px 0;
}

.temp-message {
  position: fixed;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  z-index: 1000;
  transition: opacity 0.5s;
}

.event-controls {
  margin: 10px 0;
  padding: 10px;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
}

.scaling-toggle-btn {
  margin-left: 20px;
  padding: 6px 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: bold;
  transition: background-color 0.3s;
}

.scaling-toggle-btn:hover {
  background-color: #45a049;
}

.scaling-toggle-btn:active {
  background-color: #3d8b40;
}

.upload-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
  border: 1px solid #ddd;
}

.upload-input {
  display: none;
}

.upload-btn {
  padding: 8px 15px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

.upload-btn:hover {
  background-color: #45a049;
}

.file-list {
  margin-top: 10px;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  max-height: 150px;
  overflow-y: auto;
}

.file-item {
  padding: 4px 0;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-item-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-item-actions {
  margin-left: 10px;
}

.delete-file-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  padding: 2px 5px;
  font-size: 10px;
}

.delete-file-btn:hover {
  background-color: #d32f2f;
}

.progress-container {
  width: 100%;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-top: 10px;
  display: none;
}

.progress-bar {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 4px;
  width: 0%;
  transition: width 0.3s;
}

/* Sidebar sections */
.sidebar-section {
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.sidebar-section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-section-toggle {
  cursor: pointer;
  font-size: 12px;
  color: #666;
}

/* Patient info section */
.patient-info {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.patient-info-item {
  margin-bottom: 5px;
  font-size: 12px;
}

.patient-info-label {
  font-weight: bold;
  display: inline-block;
  width: 80px;
}

/* Study files section */
.study-files {
  margin-bottom: 15px;
}

.add-files-btn {
  width: 100%;
  padding: 5px;
  margin-top: 10px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-files-btn:hover {
  background-color: #0b7dda;
}

/* Annotation tooltip */
.annotation-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 8px;
  border-radius: 3px;
  font-size: 11px;
  z-index: 100;
  pointer-events: none;
}

.signal-tooltip {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  z-index: 1000;
  pointer-events: none;
  border-left: 3px solid #2196F3;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
  max-width: 200px;
  transform: translate3d(0, 0, 0);
  /* Force hardware acceleration for smoother movement */
  transition: opacity 0.1s ease-out;
  /* Smooth fade in/out */
}

/* Drag and drop styles for signal groups */
.signal-canvas.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.signal-canvas.drag-over {
  border-top: 3px solid #2196F3;
}

.signal-drag-handle {
  position: absolute;
  top: 5px;
  right: 10px;
  width: 20px;
  height: 20px;
  cursor: grab;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666"><path d="M9 3h2v2H9V3zm4 0h2v2h-2V3zM9 7h2v2H9V7zm4 0h2v2h-2V7zm-4 4h2v2H9v-2zm4 0h2v2h-2v-2zm-4 4h2v2H9v-2zm4 0h2v2h-2v-2zm-4 4h2v2H9v-2zm4 0h2v2h-2v-2z"/></svg>') no-repeat center;
  background-size: 16px 16px;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.signal-drag-handle:hover {
  opacity: 1;
  cursor: grabbing;
}

.signal-canvas:hover .signal-drag-handle {
  opacity: 0.8;
}

/* Time navigation controls */
.time-navigation {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 10px 0;
  padding: 10px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.time-navigation label {
  font-weight: bold;
  margin-right: 5px;
}

.time-navigation input[type="number"] {
  width: 80px;
  padding: 4px 6px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.time-navigation button {
  padding: 4px 8px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.time-navigation button:hover {
  background-color: #0b7dda;
}

/* Annotation search */
.annotation-search {
  width: 100%;
  margin-bottom: 10px;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 12px;
}