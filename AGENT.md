# DICOM Viewer Agent Instructions

## Commands
- **Run app**: `python src/app.py` (Flask web app on port 5002)
- **Run standalone**: `python src/standalone_app.py` (PyWebView desktop app)
- **Test single file**: `python test_edf_support.py` 
- **Test all**: `python -m unittest discover -s . -p "test*.py"`
- **Build executable**: `python src/build_executable.py` (Windows only, creates PyInstaller executable)

## Architecture
- **Flask backend** (`src/app.py`): REST API for file upload, DICOM/EDF processing, signal extraction
- **Standalone app** (`src/standalone_app.py`): Desktop version using PyWebView with file dialogs
- **Frontend**: HTML/CSS/JS templates in `src/templates/` and `src/static/`
- **Data support**: DICOM (.dcm) and EDF (.edf) medical signal files
- **Dependencies**: Flask, pydicom, numpy, pyedflib (optional), pywebview (standalone)

## Code Style
- **Imports**: Standard library first, then third-party, then local modules
- **Functions**: Snake_case naming, docstrings for complex functions
- **Error handling**: Try-except with meaningful error messages, graceful degradation for optional features
- **File detection**: Extension-based first (.dcm, .edf), then content-based fallback
- **Patient data**: Always check hasattr() before accessing DICOM attributes
- **Temp files**: Use uuid for unique IDs, tempfile module for temp directories
