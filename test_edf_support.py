#!/usr/bin/env python3
"""
Test script to verify EDF support in the DICOM viewer
"""

import sys
import os
sys.path.append('src')

from app import detect_file_type, EDF_SUPPORT

def test_edf_support():
    """Test if EDF support is available"""
    print("Testing EDF support...")
    print(f"EDF_SUPPORT: {EDF_SUPPORT}")
    
    if EDF_SUPPORT:
        print("✓ pyedflib is available")
        try:
            import pyedflib
            print(f"✓ pyedflib version: {pyedflib.__version__}")
        except Exception as e:
            print(f"✗ Error importing pyedflib: {e}")
    else:
        print("✗ pyedflib is not available")
    
    # Test file type detection
    print("\nTesting file type detection...")
    
    # Test with fake file paths
    test_files = [
        "test.dcm",
        "test.edf", 
        "test.txt",
        "signal.EDF",
        "annotation.dcm"
    ]
    
    for test_file in test_files:
        # Create a temporary file for testing
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            
            file_type = detect_file_type(test_file)
            print(f"  {test_file} -> {file_type}")
            
            # Clean up
            os.remove(test_file)
        except Exception as e:
            print(f"  {test_file} -> Error: {e}")

if __name__ == "__main__":
    test_edf_support()
